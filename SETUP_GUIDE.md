# Complete Open-Source Cold Email Automation Setup Guide

## 🎉 Congratulations! Your Setup is Complete

All services are now running and ready to use. Here's your complete open-source cold email automation stack:

### 🔗 Service Access URLs

| Service | URL | Purpose |
|---------|-----|---------|
| **N8N (Main Interface)** | http://localhost:5678 | Workflow automation platform |
| **Ollama AI** | http://localhost:11434 | Local AI for email generation |
| **Google Maps Scraper** | http://localhost:3007 | Business data extraction |
| **File Server** | http://localhost:8080 | Data file management |
| **Mailtrain** | http://localhost:3000 | Email marketing platform |
| **MongoDB** | localhost:27017 | Document database |
| **MySQL** | localhost:3307 | Relational database |
| **Redis** | localhost:6379 | Cache & session storage |

### 📋 Quick Start Steps

1. **Access N8N**: Open http://localhost:5678 in your browser
2. **Import Workflow**: Import the `workflow-cold-email-opensource.json` file
3. **Configure Credentials**: Set up your email and API credentials
4. **Test the Workflow**: Run a test with sample data

### 🔧 Pre-configured Features

#### ✅ What's Already Set Up:
- ✅ N8N with web scraping capabilities (Selenium + Chrome)
- ✅ Ollama AI with Llama 3.1 model (4.9 GB downloaded)
- ✅ Google Maps business scraper
- ✅ All databases (MongoDB, MySQL, PostgreSQL, Redis)
- ✅ Email marketing platform (Mailtrain)
- ✅ File server for data management
- ✅ Complete Docker orchestration

#### 🛠 What You Need to Configure:

1. **Hunter.io API** (Free tier - 100 searches/month)
   - Sign up at https://hunter.io
   - Get your API key
   - Add to N8N credentials

2. **SMTP Email Settings**
   - Configure your email provider
   - Add credentials to N8N

3. **Business Data Sources**
   - Import your target business list
   - Configure Google Maps search parameters

### 📁 Workflow Files

| File | Description |
|------|-------------|
| `workflow-cold-email-opensource.json` | Complete open-source workflow |
| `workflow-cold-email-n8n.json` | Original (with paid services) |
| `scraper.py` | Custom web scraper (Selenium-based) |

### 🔄 Workflow Components

#### 1. Business Discovery
- **Google Maps Scraper**: Extract business listings by location/category
- **Data Processing**: Clean and structure business information
- **CSV Storage**: Save data locally for processing

#### 2. Contact Information
- **Hunter.io Integration**: Find email addresses (free tier)
- **Custom Web Scraper**: Extract contact details from websites
- **Email Validation**: Verify email deliverability

#### 3. AI-Powered Email Generation
- **Ollama AI (Llama 3.1)**: Generate personalized emails
- **Business Context**: Use scraped data for personalization
- **Template System**: Consistent email formatting

#### 4. Email Delivery
- **SMTP Integration**: Send emails via your provider
- **Tracking**: Monitor delivery and responses
- **Scheduling**: Automated sending with delays

### 🚀 Running Your First Campaign

1. **Import Workflow**:
   ```
   - Go to N8N (http://localhost:5678)
   - Click "Import from File"
   - Select "workflow-cold-email-opensource.json"
   ```

2. **Configure Credentials**:
   ```
   - Hunter.io API key
   - SMTP email settings
   - Ollama AI connection (already configured)
   ```

3. **Set Target Parameters**:
   ```
   - Business category (e.g., "restaurants")
   - Location (e.g., "New York, NY")
   - Email template preferences
   ```

4. **Execute Workflow**:
   ```
   - Start with test mode
   - Monitor logs for errors
   - Scale up once validated
   ```

### 📊 Data Flow

```
Business Search → Contact Discovery → Email Generation → Delivery
      ↓               ↓                    ↓              ↓
 Google Maps      Hunter.io         Ollama AI        SMTP
   Scraper       Web Scraper      (Llama 3.1)     Provider
      ↓               ↓                    ↓              ↓
   CSV Files      Email Lists      Personalized    Tracking
                                    Content        & Reports
```

### 🛡 Privacy & Compliance

- **Local AI**: All email generation happens locally (no external AI APIs)
- **Data Storage**: All data stored locally in CSV files
- **GDPR Compliance**: Respect opt-out requests and data regulations
- **Rate Limiting**: Built-in delays to respect website terms

### 📈 Scaling Your Operation

#### Free Tier Limits:
- **Hunter.io**: 100 email searches/month
- **Local AI**: Unlimited (runs on your hardware)
- **Web Scraping**: Unlimited (rate-limited for politeness)

#### Upgrade Options:
- **Hunter.io Pro**: More email searches
- **Better Hardware**: Faster AI processing
- **Multiple Containers**: Parallel processing

### 🔧 Troubleshooting

#### Common Issues:

1. **N8N Not Loading**:
   ```bash
   docker compose logs n8n
   ```

2. **Ollama AI Errors**:
   ```bash
   docker exec mysetupn8n-ollama-1 ollama list
   ```

3. **Scraper Issues**:
   ```bash
   docker exec mysetupn8n-n8n-1 python /app/scraper.py --test
   ```

#### Restart Services:
```bash
docker compose down
docker compose up -d
```

### 💡 Optimization Tips

1. **Performance**:
   - Adjust Ollama model size based on hardware
   - Optimize scraping delays
   - Use batch processing for large lists

2. **Quality**:
   - Refine AI prompts for better emails
   - A/B test subject lines
   - Monitor response rates

3. **Compliance**:
   - Implement unsubscribe handling
   - Respect robots.txt files
   - Add proper email headers

### 🆘 Support Resources

- **N8N Documentation**: https://docs.n8n.io/
- **Ollama Documentation**: https://ollama.ai/docs
- **Docker Compose**: https://docs.docker.com/compose/

### 🎯 Next Steps

1. **Import the workflow** into N8N
2. **Configure your credentials** (Hunter.io, SMTP)
3. **Test with a small dataset** first
4. **Scale up** once everything works
5. **Monitor and optimize** performance

---

## 🔥 You're All Set!

Your complete open-source cold email automation system is ready. No more paid APIs or external dependencies - everything runs locally and privately on your machine.

**Total Cost**: $0/month (excluding your email provider)
**Privacy**: 100% local processing
**Scalability**: Limited only by your hardware

Happy automating! 🚀
