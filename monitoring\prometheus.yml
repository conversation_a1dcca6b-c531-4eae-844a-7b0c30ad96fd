# Prometheus Configuration for N8N Cold Email Monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # N8N Metrics
  - job_name: 'n8n'
    static_configs:
      - targets: ['n8n:5678']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Ollama Performance
  - job_name: 'ollama'
    static_configs:
      - targets: ['ollama:11434']
    metrics_path: '/metrics'
    scrape_interval: 60s

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # System Metrics
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s

  # Email Deliverability Monitoring
  - job_name: 'postfix'
    static_configs:
      - targets: ['postfix:9154']
    scrape_interval: 60s

  # Custom Workflow Metrics
  - job_name: 'cold-email-workflow'
    static_configs:
      - targets: ['n8n:5678']
    metrics_path: '/webhook/workflow-metrics'
    scrape_interval: 300s  # 5 minutes
