#!/usr/bin/env python3
"""
Test Actual System Components
Tests the real components and data files of the Real Estate Cold Email system
"""

import json
import os
import sys
from pathlib import Path

def test_data_files():
    """Test that all required data files exist and are valid"""
    print("🔍 Testing Data Files...")
    
    data_files = [
        "data/real_estate_emails.json",
        "data/neuromarketing_prompts.json",
        "data/competitor_analysis.json",
        "data/sample_data_seeds.json"
    ]
    
    results = {}
    
    for file_path in data_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                print(f"✅ {file_path}: {len(data)} items loaded")
                results[file_path] = {"exists": True, "valid": True, "count": len(data)}
            except Exception as e:
                print(f"❌ {file_path}: Invalid JSON - {e}")
                results[file_path] = {"exists": True, "valid": False, "error": str(e)}
        else:
            print(f"⚠️  {file_path}: File not found")
            results[file_path] = {"exists": False, "valid": False}
    
    return results

def test_script_files():
    """Test that all required script files exist"""
    print("\n🔍 Testing Script Files...")
    
    script_files = [
        "scripts/deploy_model.py",
        "scripts/fine_tune_real_estate.py",
        "scripts/backup-system.sh",
        "scripts/health-check.sh",
        "scripts/resource_optimization.py"
    ]
    
    results = {}
    
    for file_path in script_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: Available")
            results[file_path] = {"exists": True}
        else:
            print(f"❌ {file_path}: Missing")
            results[file_path] = {"exists": False}
    
    return results

def test_configuration_files():
    """Test configuration files"""
    print("\n🔍 Testing Configuration Files...")
    
    config_files = [
        "docker-compose.yml",
        ".env.example",
        "configs/fine_tuning_config.json",
        "configs/prometheus.yml",
        "configs/grafana_dashboard.json"
    ]
    
    results = {}
    
    for file_path in config_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: Available")
            results[file_path] = {"exists": True}
        else:
            print(f"❌ {file_path}: Missing")
            results[file_path] = {"exists": False}
    
    return results

def test_docker_services():
    """Test Docker services status"""
    print("\n🔍 Testing Docker Services...")
    
    try:
        import subprocess
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # Header + at least one container
                print(f"✅ Docker is running with {len(lines)-1} containers")
                
                # Check for specific services
                services = ['n8n', 'postgres', 'redis', 'ollama', 'grafana', 'prometheus']
                running_services = []
                
                for line in lines[1:]:  # Skip header
                    for service in services:
                        if service in line.lower():
                            running_services.append(service)
                
                for service in services:
                    if service in running_services:
                        print(f"✅ {service}: Running")
                    else:
                        print(f"⚠️  {service}: Not detected")
                
                return {"docker_available": True, "running_services": running_services}
            else:
                print("⚠️  Docker is running but no containers found")
                return {"docker_available": True, "running_services": []}
        else:
            print("❌ Docker is not running or not accessible")
            return {"docker_available": False, "running_services": []}
    except Exception as e:
        print(f"❌ Error checking Docker: {e}")
        return {"docker_available": False, "error": str(e)}

def test_psychology_framework():
    """Test psychology framework components"""
    print("\n🔍 Testing Psychology Framework...")
    
    # Test psychology triggers
    psychology_triggers = ["reciprocity", "scarcity", "social_proof", "loss_aversion", "anchoring"]
    buyer_personas = ["luxury_buyer", "investor", "first_time_buyer", "empty_nester", "tech_professional"]
    
    print(f"✅ Psychology triggers defined: {len(psychology_triggers)}")
    for trigger in psychology_triggers:
        print(f"   • {trigger}")
    
    print(f"✅ Buyer personas defined: {len(buyer_personas)}")
    for persona in buyer_personas:
        print(f"   • {persona}")
    
    # Test trigger-persona combinations
    optimal_combinations = {
        "luxury_buyer": ["scarcity", "anchoring", "social_proof"],
        "investor": ["loss_aversion", "anchoring"],
        "first_time_buyer": ["reciprocity", "social_proof"],
        "empty_nester": ["reciprocity", "social_proof"],
        "tech_professional": ["scarcity", "anchoring"]
    }
    
    print("✅ Optimal trigger combinations:")
    for persona, triggers in optimal_combinations.items():
        print(f"   • {persona}: {', '.join(triggers)}")
    
    return {
        "psychology_triggers": psychology_triggers,
        "buyer_personas": buyer_personas,
        "optimal_combinations": optimal_combinations
    }

def test_email_templates():
    """Test email template structure"""
    print("\n🔍 Testing Email Templates...")
    
    # Test if we can load and validate email templates
    if os.path.exists("data/real_estate_emails.json"):
        try:
            with open("data/real_estate_emails.json", 'r') as f:
                templates = json.load(f)
            
            print(f"✅ Email templates loaded: {len(templates)} templates")
            
            # Validate template structure
            required_fields = ["buyer_persona", "subject_line", "email_body", "psychology_triggers"]
            valid_templates = 0
            
            for i, template in enumerate(templates[:5]):  # Check first 5
                missing_fields = [field for field in required_fields if field not in template]
                if not missing_fields:
                    valid_templates += 1
                    print(f"✅ Template {i+1}: Valid structure")
                else:
                    print(f"❌ Template {i+1}: Missing fields: {missing_fields}")
            
            return {
                "templates_loaded": len(templates),
                "valid_templates": valid_templates,
                "structure_valid": valid_templates > 0
            }
        except Exception as e:
            print(f"❌ Error loading email templates: {e}")
            return {"error": str(e)}
    else:
        print("❌ Email templates file not found")
        return {"templates_loaded": 0}

def test_compliance_framework():
    """Test compliance framework"""
    print("\n🔍 Testing Compliance Framework...")
    
    compliance_requirements = {
        "GDPR": ["consent_tracking", "data_protection", "right_to_erasure"],
        "CAN-SPAM": ["sender_identification", "unsubscribe_mechanism", "physical_address"],
        "Real Estate": ["license_disclosure", "fair_housing_compliance", "truthful_advertising"]
    }
    
    print("✅ Compliance requirements defined:")
    for regulation, requirements in compliance_requirements.items():
        print(f"   • {regulation}: {len(requirements)} requirements")
        for req in requirements:
            print(f"     - {req}")
    
    return compliance_requirements

def run_comprehensive_component_test():
    """Run all component tests"""
    print("🚀 COMPREHENSIVE COMPONENT TESTING")
    print("=" * 60)
    
    results = {}
    
    # Test all components
    results["data_files"] = test_data_files()
    results["script_files"] = test_script_files()
    results["config_files"] = test_configuration_files()
    results["docker_services"] = test_docker_services()
    results["psychology_framework"] = test_psychology_framework()
    results["email_templates"] = test_email_templates()
    results["compliance_framework"] = test_compliance_framework()
    
    # Generate summary
    print("\n" + "=" * 60)
    print("📊 COMPONENT TEST SUMMARY")
    print("=" * 60)
    
    total_tests = 0
    passed_tests = 0
    
    # Count data files
    for file_path, result in results["data_files"].items():
        total_tests += 1
        if result.get("exists") and result.get("valid"):
            passed_tests += 1
    
    # Count script files
    for file_path, result in results["script_files"].items():
        total_tests += 1
        if result.get("exists"):
            passed_tests += 1
    
    # Count config files
    for file_path, result in results["config_files"].items():
        total_tests += 1
        if result.get("exists"):
            passed_tests += 1
    
    # Docker services
    total_tests += 1
    if results["docker_services"].get("docker_available"):
        passed_tests += 1
    
    # Framework components
    total_tests += 3  # Psychology, templates, compliance
    if results["psychology_framework"]:
        passed_tests += 1
    if results["email_templates"].get("structure_valid"):
        passed_tests += 1
    if results["compliance_framework"]:
        passed_tests += 1
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"Total Component Tests: {total_tests}")
    print(f"Tests Passed: {passed_tests}")
    print(f"Tests Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS")
    print("-" * 30)
    
    if success_rate >= 90:
        print("✅ Excellent! All major components are in place and functional.")
    elif success_rate >= 75:
        print("⚠️  Good component coverage with some missing files.")
    else:
        print("❌ Several components are missing. Review the setup.")
    
    # Specific recommendations
    missing_data_files = [f for f, r in results["data_files"].items() if not r.get("exists")]
    if missing_data_files:
        print(f"• Missing data files: {len(missing_data_files)}")
    
    missing_scripts = [f for f, r in results["script_files"].items() if not r.get("exists")]
    if missing_scripts:
        print(f"• Missing script files: {len(missing_scripts)}")
    
    if not results["docker_services"].get("docker_available"):
        print("• Docker is not running - start Docker for full system testing")
    
    # Save results
    results_file = Path("test_results") / f"component_test_results_{Path(__file__).stem}.json"
    results_file.parent.mkdir(exist_ok=True)
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return results

if __name__ == "__main__":
    run_comprehensive_component_test()
