#!/usr/bin/env python3
"""
Test script for the open-source cold email automation workflow
"""

import requests
import json
import time

def test_workflow():
    """Test the complete workflow"""
    
    print("🧪 Testing Open Source Cold Email Automation Workflow")
    print("=" * 60)
    
    # Test 1: Check if all services are running
    services = {
        "N8N": "http://localhost:5678",
        "Ollama": "http://localhost:11434",
        "Google Maps Scraper": "http://localhost:3007",
        "Mailtrain": "http://localhost:3000",
        "File Server": "http://localhost:8080"
    }
    
    print("\n1. 🔍 Checking service availability...")
    for service, url in services.items():
        try:
            response = requests.get(url, timeout=5)
            status = "✅ Running" if response.status_code < 400 else "⚠️ Issues"
            print(f"   {service}: {status}")
        except Exception as e:
            print(f"   {service}: ❌ Not accessible ({str(e)[:50]})")
    
    # Test 2: Test Ollama AI
    print("\n2. 🤖 Testing Ollama AI generation...")
    try:
        ollama_data = {
            "model": "llama3.1:8b",
            "prompt": "Generate a professional 3-sentence cold email for a restaurant in New York.",
            "stream": False
        }
        response = requests.post("http://localhost:11434/api/generate", 
                               json=ollama_data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print("   ✅ AI email generation working")
            print(f"   📧 Sample: {result.get('response', 'No response')[:100]}...")
        else:
            print(f"   ❌ AI generation failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ AI generation error: {str(e)}")
    
    # Test 3: Test Google Maps Scraper
    print("\n3. 🗺️ Testing Google Maps Scraper...")
    try:
        scraper_data = {
            "query": "restaurants in San Francisco",
            "zoomStrategy": "auto-grid"
        }
        response = requests.post("http://localhost:3007/api/search", 
                               json=scraper_data, timeout=20)
        if response.status_code == 200:
            print("   ✅ Google Maps scraper working")
        else:
            print(f"   ❌ Scraper failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Scraper error: {str(e)}")
    
    # Test 4: Test N8N Webhook
    print("\n4. 🔗 Testing N8N Webhook...")
    try:
        webhook_data = {
            "gmaps_query": "coffee shops in Boston"
        }
        response = requests.post("http://localhost:5678/webhook/start-scrape", 
                               json=webhook_data, timeout=10)
        if response.status_code < 400:
            print("   ✅ N8N webhook accessible")
        else:
            print(f"   ⚠️ Webhook returned: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Webhook error: {str(e)}")
    
    # Test 5: Check data file
    print("\n5. 📁 Checking data file...")
    try:
        response = requests.get("http://localhost:8080/leads.csv", timeout=5)
        if response.status_code == 200:
            lines = response.text.strip().split('\n')
            print(f"   ✅ CSV file accessible ({len(lines)} lines)")
        else:
            print(f"   ❌ CSV file not accessible: {response.status_code}")
    except Exception as e:
        print(f"   ❌ File server error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\n💡 Next steps:")
    print("   1. Import workflow-cold-email-opensource.json into N8N")
    print("   2. Configure your SMTP credentials")
    print("   3. Add Hunter.io API key for email discovery")
    print("   4. Run a test with real data")
    print("\n📚 See README-opensource.md for detailed setup instructions")

if __name__ == "__main__":
    test_workflow()
