#!/bin/bash

# 🚀 QUICK START: Open-Source Email Finding System
# This script demonstrates immediate usage of the Google Maps email extraction

echo "🚀 OPEN-SOURCE EMAIL FINDER - QUICK START"
echo "=========================================="
echo ""

# Function to run email extraction
run_email_extraction() {
    local query="$1"
    local count="${2:-10}"
    
    echo "🔍 Searching for: $query"
    echo "📊 Processing: $count businesses"
    echo "⏳ Please wait..."
    echo ""
    
    # Run Google Maps scraper with email extraction
    docker exec mysetupn8n-gosom-scraper-1 bash -c "
        echo '$query' > /tmp/query.txt && 
        google-maps-scraper -input /tmp/query.txt -c $count -email -depth 3 -json 2>/dev/null
    " | python3 -c "
import json
import sys

try:
    data = []
    for line in sys.stdin:
        line = line.strip()
        if line.startswith('{') and 'title' in line:
            try:
                business = json.loads(line)
                data.append(business)
            except:
                continue
    
    print(f'📈 RESULTS SUMMARY')
    print(f'==================')
    print(f'Total Businesses Found: {len(data)}')
    
    businesses_with_emails = 0
    total_emails = 0
    
    for i, business in enumerate(data, 1):
        name = business.get('title', 'Unknown Business')
        emails = business.get('emails', [])
        website = business.get('web_site', 'N/A')
        rating = business.get('review_rating', 'N/A')
        
        if emails:
            businesses_with_emails += 1
            total_emails += len(emails)
            
            print(f'\\n✅ {i}. {name}')
            print(f'   📧 Emails: {\", \".join(emails)}')
            print(f'   🌐 Website: {website}')
            print(f'   ⭐ Rating: {rating}/5')
        else:
            print(f'\\n❌ {i}. {name} (No emails found)')
            print(f'   🌐 Website: {website}')
            print(f'   ⭐ Rating: {rating}/5')
    
    success_rate = (businesses_with_emails / len(data) * 100) if data else 0
    
    print(f'\\n📊 PERFORMANCE METRICS')
    print(f'======================')
    print(f'Businesses with Emails: {businesses_with_emails}/{len(data)}')
    print(f'Total Emails Found: {total_emails}')
    print(f'Success Rate: {success_rate:.1f}%')
    print(f'Cost: $0.00 (vs Hunter.io: ~${len(data) * 0.003:.2f})')
    
except Exception as e:
    print(f'Error processing results: {e}')
"
}

# Test cases
echo "🧪 DEMO 1: Coffee Shops in Seattle"
echo "=================================="
run_email_extraction "coffee shops in Seattle" 5

echo ""
echo ""
echo "🧪 DEMO 2: Marketing Agencies in NYC"
echo "===================================="
run_email_extraction "marketing agencies in New York" 5

echo ""
echo ""
echo "🧪 DEMO 3: Restaurants in San Francisco"
echo "======================================="
run_email_extraction "restaurants in San Francisco" 5

echo ""
echo ""
echo "🎯 CUSTOM USAGE"
echo "==============="
echo "To run your own search:"
echo "  ./quick_start_email_finder.sh \"your search query\" 10"
echo ""
echo "Examples:"
echo "  ./quick_start_email_finder.sh \"law firms in Chicago\" 20"
echo "  ./quick_start_email_finder.sh \"dentists in Miami\" 15"
echo "  ./quick_start_email_finder.sh \"real estate agents in Austin\" 25"
echo ""
echo "🚀 PRODUCTION INTEGRATION"
echo "========================="
echo "1. Import workflow-cold-email-complete-opensource.json into N8N"
echo "2. Visit http://localhost:5678 to access N8N interface"
echo "3. Use this command pattern in N8N Execute Command nodes"
echo "4. Scale up by increasing the -c parameter"
echo ""
echo "💰 COST COMPARISON"
echo "=================="
echo "Hunter.io: $34/year + $0.003 per email"
echo "Our Solution: $0 forever + unlimited emails"
echo ""
echo "🏆 YOU NOW HAVE A SUPERIOR, FREE EMAIL FINDING SYSTEM!"
echo "======================================================"
