#!/usr/bin/env python3
"""
Enhanced <PERSON>ail Finder - Open Source Alternative to Hunter.io
Combines multiple methods for comprehensive email discovery
"""

import re
import requests
import time
import dns.resolver
import socket
import smtplib
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from typing import List, Set, Dict, Optional
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailFinder:
    """
    Comprehensive email finder using multiple sources and methods
    """
    
    def __init__(self, headless=True, timeout=10):
        self.timeout = timeout
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.setup_driver(headless)
        
        # Common email prefixes for pattern generation
        self.common_prefixes = [
            'info', 'contact', 'hello', 'sales', 'support', 'admin',
            'marketing', 'business', 'office', 'team', 'help',
            'inquiry', 'general', 'service', 'customerservice'
        ]
        
        # Pages to check for emails
        self.email_pages = [
            '/', '/contact', '/about', '/contact-us', '/about-us',
            '/team', '/staff', '/privacy', '/legal', '/careers',
            '/support', '/help', '/services'
        ]
    
    def setup_driver(self, headless=True):
        """Setup Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(self.timeout)
        except Exception as e:
            logger.error(f"Failed to setup Chrome driver: {e}")
            raise
    
    def extract_emails_from_text(self, text: str) -> Set[str]:
        """Extract emails from text using regex"""
        emails = set()
        if text:
            found_emails = self.email_pattern.findall(text)
            # Filter out common false positives
            for email in found_emails:
                email = email.lower().strip()
                if self.is_valid_email(email):
                    emails.add(email)
        return emails
    
    def is_valid_email(self, email: str) -> bool:
        """Basic email validation"""
        if not email or '@' not in email:
            return False
        
        # Filter out common false positives
        false_positives = [
            '.jpg', '.png', '.gif', '.pdf', '.doc', '.zip',
            'example.com', 'domain.com', 'yoursite.com',
            'noreply@', 'no-reply@', 'donotreply@'
        ]
        
        email_lower = email.lower()
        for fp in false_positives:
            if fp in email_lower:
                return False
        
        return True
    
    def get_domain_from_url(self, url: str) -> str:
        """Extract domain from URL"""
        try:
            parsed = urlparse(url)
            return parsed.netloc.lower().replace('www.', '')
        except:
            return ""
    
    def generate_email_patterns(self, business_name: str, domain: str, contact_name: str = None) -> List[str]:
        """Generate common email patterns"""
        emails = []
        domain = domain.lower()
        
        # Common business emails
        for prefix in self.common_prefixes:
            emails.append(f"{prefix}@{domain}")
        
        # If we have contact name, generate personal emails
        if contact_name:
            name_parts = contact_name.lower().split()
            if len(name_parts) >= 2:
                first = name_parts[0]
                last = name_parts[-1]
                
                patterns = [
                    f"{first}@{domain}",
                    f"{last}@{domain}",
                    f"{first}.{last}@{domain}",
                    f"{first}_{last}@{domain}",
                    f"{first[0]}.{last}@{domain}",
                    f"{first}.{last[0]}@{domain}",
                    f"{first}{last}@{domain}",
                    f"{last}.{first}@{domain}"
                ]
                emails.extend(patterns)
        
        return list(set(emails))  # Remove duplicates
    
    def verify_email_exists(self, email: str) -> bool:
        """
        Verify if email exists using SMTP verification
        Note: Some servers block this, so use cautiously
        """
        try:
            domain = email.split('@')[1]
            mx_records = dns.resolver.resolve(domain, 'MX')
            mx_record = str(mx_records[0].exchange)
            
            # Connect to mail server
            server = smtplib.SMTP(timeout=10)
            server.connect(mx_record)
            server.helo()
            server.mail('<EMAIL>')
            code, message = server.rcpt(email)
            server.quit()
            
            return code == 250
        except:
            return False  # Assume valid if we can't verify
    
    def scrape_website_emails(self, url: str) -> Set[str]:
        """Scrape emails from website using multiple methods"""
        emails = set()
        domain = self.get_domain_from_url(url)
        
        # Ensure URL has protocol
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        logger.info(f"Scraping emails from: {url}")
        
        try:
            # Method 1: Check multiple pages
            for page in self.email_pages:
                page_url = urljoin(url, page)
                page_emails = self.scrape_single_page(page_url)
                emails.update(page_emails)
                
                # Don't overwhelm the server
                time.sleep(1)
            
        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
        
        # Filter emails to only include those from the same domain or common business emails
        filtered_emails = set()
        for email in emails:
            email_domain = email.split('@')[1] if '@' in email else ''
            if email_domain == domain or any(prefix in email for prefix in self.common_prefixes):
                filtered_emails.add(email)
        
        return filtered_emails
    
    def scrape_single_page(self, url: str) -> Set[str]:
        """Scrape emails from a single page"""
        emails = set()
        
        try:
            logger.info(f"Checking page: {url}")
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 5).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Method 1: Extract from page source
            page_source = self.driver.page_source
            emails.update(self.extract_emails_from_text(page_source))
            
            # Method 2: Look for mailto links
            try:
                mailto_links = self.driver.find_elements(By.XPATH, "//a[starts-with(@href, 'mailto:')]")
                for link in mailto_links:
                    href = link.get_attribute('href')
                    if href and href.startswith('mailto:'):
                        email = href.replace('mailto:', '').split('?')[0]
                        if self.is_valid_email(email):
                            emails.add(email.lower())
            except:
                pass
            
            # Method 3: Look for contact forms and nearby text
            try:
                contact_sections = self.driver.find_elements(By.XPATH, 
                    "//*[contains(text(), 'contact') or contains(text(), 'email') or contains(text(), '@')]")
                for section in contact_sections:
                    section_text = section.text
                    emails.update(self.extract_emails_from_text(section_text))
            except:
                pass
                
        except TimeoutException:
            logger.warning(f"Timeout loading page: {url}")
        except Exception as e:
            logger.warning(f"Error scraping page {url}: {e}")
        
        return emails
    
    def search_google_for_emails(self, business_name: str, domain: str) -> Set[str]:
        """
        Search Google for emails using site: operator
        Note: This should be used sparingly to avoid rate limiting
        """
        emails = set()
        
        # Google search queries
        queries = [
            f'site:{domain} "email" OR "contact"',
            f'site:{domain} "@{domain}"',
            f'"{business_name}" email site:{domain}',
            f'filetype:pdf site:{domain} email'
        ]
        
        # This is a placeholder - implementing Google search requires
        # either Google Custom Search API (which has limits) or 
        # scraping Google (which can get blocked)
        # For now, we'll skip this method
        
        return emails
    
    def find_emails_comprehensive(self, business_name: str, website_url: str, contact_name: str = None) -> Dict:
        """
        Comprehensive email finding using all methods
        """
        logger.info(f"Finding emails for {business_name} - {website_url}")
        
        all_emails = set()
        methods_used = []
        domain = self.get_domain_from_url(website_url)
        
        # Method 1: Website scraping
        try:
            scraped_emails = self.scrape_website_emails(website_url)
            all_emails.update(scraped_emails)
            if scraped_emails:
                methods_used.append(f"website_scraping ({len(scraped_emails)} found)")
        except Exception as e:
            logger.error(f"Website scraping failed: {e}")
        
        # Method 2: Email pattern generation
        try:
            pattern_emails = self.generate_email_patterns(business_name, domain, contact_name)
            # We'll mark these as "generated" patterns, not verified
            methods_used.append(f"pattern_generation ({len(pattern_emails)} generated)")
        except Exception as e:
            logger.error(f"Pattern generation failed: {e}")
            pattern_emails = []
        
        # Method 3: Google/OSINT search (placeholder for now)
        # osint_emails = self.search_google_for_emails(business_name, domain)
        # all_emails.update(osint_emails)
        
        result = {
            'business_name': business_name,
            'domain': domain,
            'website_url': website_url,
            'found_emails': list(all_emails),
            'generated_patterns': pattern_emails,
            'total_found': len(all_emails),
            'total_patterns': len(pattern_emails),
            'methods_used': methods_used,
            'success': len(all_emails) > 0 or len(pattern_emails) > 0
        }
        
        logger.info(f"Email search complete: {len(all_emails)} found, {len(pattern_emails)} patterns generated")
        return result
    
    def __del__(self):
        """Cleanup"""
        try:
            if hasattr(self, 'driver'):
                self.driver.quit()
        except:
            pass

def main():
    """Test the email finder"""
    finder = EmailFinder(headless=True)
    
    # Test cases
    test_cases = [
        {
            'business_name': 'OpenAI',
            'website_url': 'https://openai.com',
            'contact_name': 'Sam Altman'
        },
        {
            'business_name': 'Anthropic',
            'website_url': 'https://anthropic.com',
            'contact_name': None
        }
    ]
    
    for test in test_cases:
        print(f"\n{'='*60}")
        print(f"Testing: {test['business_name']}")
        print(f"{'='*60}")
        
        result = finder.find_emails_comprehensive(
            test['business_name'],
            test['website_url'],
            test['contact_name']
        )
        
        print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
