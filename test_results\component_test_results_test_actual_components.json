{"data_files": {"data/real_estate_emails.json": {"exists": true, "valid": true, "count": 5}, "data/neuromarketing_prompts.json": {"exists": true, "valid": true, "count": 4}, "data/competitor_analysis.json": {"exists": false, "valid": false}, "data/sample_data_seeds.json": {"exists": false, "valid": false}}, "script_files": {"scripts/deploy_model.py": {"exists": true}, "scripts/fine_tune_real_estate.py": {"exists": true}, "scripts/backup-system.sh": {"exists": true}, "scripts/health-check.sh": {"exists": true}, "scripts/resource_optimization.py": {"exists": true}}, "config_files": {"docker-compose.yml": {"exists": false}, ".env.example": {"exists": true}, "configs/fine_tuning_config.json": {"exists": true}, "configs/prometheus.yml": {"exists": false}, "configs/grafana_dashboard.json": {"exists": false}}, "docker_services": {"docker_available": true, "running_services": ["n8n", "ollama", "n8n", "postgres", "redis"]}, "psychology_framework": {"psychology_triggers": ["reciprocity", "scarcity", "social_proof", "loss_aversion", "anchoring"], "buyer_personas": ["luxury_buyer", "investor", "first_time_buyer", "empty_nester", "tech_professional"], "optimal_combinations": {"luxury_buyer": ["scarcity", "anchoring", "social_proof"], "investor": ["loss_aversion", "anchoring"], "first_time_buyer": ["reciprocity", "social_proof"], "empty_nester": ["reciprocity", "social_proof"], "tech_professional": ["scarcity", "anchoring"]}}, "email_templates": {"templates_loaded": 5, "valid_templates": 5, "structure_valid": true}, "compliance_framework": {"GDPR": ["consent_tracking", "data_protection", "right_to_erasure"], "CAN-SPAM": ["sender_identification", "unsubscribe_mechanism", "physical_address"], "Real Estate": ["license_disclosure", "fair_housing_compliance", "truthful_advertising"]}}