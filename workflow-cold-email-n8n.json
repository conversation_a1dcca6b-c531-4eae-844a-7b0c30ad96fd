{"name": "n8n_cold_email_automation", "nodes": [{"id": "trigger", "name": "Start Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [100, 100], "parameters": {"httpMethod": "POST", "path": "start-scrape"}}, {"id": "scrape_gmaps", "name": "Scrape Google Maps", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [300, 100], "parameters": {"url": "http://localhost:3007/api/search", "method": "POST", "jsonParameters": true, "headers": {"Content-Type": "application/json"}, "body": {"query": "={{$json.gmaps_query}}", "zoomStrategy": "auto-grid"}}}, {"id": "split", "name": "Split In Batches", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 1, "position": [500, 100], "parameters": {"batchSize": 50}}, {"id": "snscrape", "name": "SNScrape Command", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [700, 100], "parameters": {"command": "snscrape twitter-search \"{{$json.name}}\" --json"}}, {"id": "scrapingdog", "name": "ScrapingDog API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [900, 100], "parameters": {"url": "https://api.scrapingdog.com/scrape", "method": "POST", "jsonParameters": true, "headers": {"Content-Type": "application/json", "api_key": "={{$credentials.scrapingdog.api_key}}"}, "body": {"url": "={{$json.website}}"}}}, {"id": "read_sheet", "name": "Read Google Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1100, 100], "parameters": {"operation": "read", "sheetId": "SHEET_ID", "range": "Leads!A2:D"}}, {"id": "filter_dupes", "name": "Filter Duplicates", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1300, 100], "parameters": {"functionCode": "const existingIds = $items('read_sheet').map(i => i.json.place_id || '');\nreturn items.filter(item => !existingIds.includes(item.json.place_id));"}}, {"id": "append_sheet", "name": "Append To Sheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1500, 100], "parameters": {"operation": "append", "sheetId": "SHEET_ID", "range": "Leads!A2:D", "options": {"valueInputMode": "RAW"}}}, {"id": "cron", "name": "Daily Cron", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [1700, 100], "parameters": {"mode": "everyDay", "hour": 9}}, {"id": "read_new", "name": "Read New Leads", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [1900, 100], "parameters": {"operation": "read", "sheetId": "SHEET_ID", "range": "Leads!A2:G", "filter": "status=new"}}, {"id": "perplexity", "name": "Perplexity API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2100, 100], "parameters": {"url": "https://api.perplexity.ai/chat/completions", "method": "POST", "jsonParameters": true, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$credentials.perplexity.api_key}}"}, "body": {"model": "mistral-7b-instruct", "max_tokens": 256, "messages": [{"role": "system", "content": "Generate a concise 3-sentence cold email."}, {"role": "user", "content": "Name: {{$json.name}}, Biz: {{$json.business}}, Loc: {{$json.location}}, Review: {{$json.top_review}}"}]}}}, {"id": "format_email", "name": "Format Email", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [2300, 100], "parameters": {"functionCode": "return items.map(i => ({ json: { email: i.json.choices[0].message.content, address: i.json.email } }));"}}, {"id": "send_email", "name": "Send Via Mailtrain", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2500, 100], "parameters": {"url": "http://localhost:3000/api/send", "method": "POST", "jsonParameters": true, "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{$credentials.mailtrain.access_token}}"}, "body": {"to": "={{$json.address}}", "html": "={{$json.email}}", "campaign": "cold-email"}}}, {"id": "webhook_events", "name": "Mailtrain Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [2700, 100], "parameters": {"httpMethod": "POST", "path": "mailtrain-events"}}, {"id": "update_status", "name": "Update Status", "type": "n8n-nodes-base.googleSheets", "typeVersion": 1, "position": [2900, 100], "parameters": {"operation": "update", "sheetId": "SHEET_ID", "range": "Leads!G:G", "valueInputMode": "RAW"}}], "connections": {"trigger": {"main": [[{"node": "scrape_gmaps", "type": "main", "index": 0}]]}, "scrape_gmaps": {"main": [[{"node": "split", "type": "main", "index": 0}]]}, "split": {"main": [[{"node": "snscrape", "type": "main", "index": 0}]]}, "snscrape": {"main": [[{"node": "scrapingdog", "type": "main", "index": 0}]]}, "scrapingdog": {"main": [[{"node": "read_sheet", "type": "main", "index": 0}]]}, "read_sheet": {"main": [[{"node": "filter_dupes", "type": "main", "index": 0}]]}, "filter_dupes": {"main": [[{"node": "append_sheet", "type": "main", "index": 0}]]}, "cron": {"main": [[{"node": "read_new", "type": "main", "index": 0}]]}, "read_new": {"main": [[{"node": "perplexity", "type": "main", "index": 0}]]}, "perplexity": {"main": [[{"node": "format_email", "type": "main", "index": 0}]]}, "format_email": {"main": [[{"node": "send_email", "type": "main", "index": 0}]]}, "send_email": {"main": [[{"node": "webhook_events", "type": "main", "index": 0}]]}, "webhook_events": {"main": [[{"node": "update_status", "type": "main", "index": 0}]]}}}