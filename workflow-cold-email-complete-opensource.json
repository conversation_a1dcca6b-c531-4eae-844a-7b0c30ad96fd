{"name": "Cold Email Automation - Complete Open Source", "nodes": [{"parameters": {"method": "POST", "url": "=http://localhost:5678/webhook/start-cold-email", "options": {"response": {"responseMode": "responseNode"}}}, "id": "webhook-start", "name": "Start Workflow", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [260, 300]}, {"parameters": {"command": "python3 /data/scripts/enhanced_email_finder.py", "additionalFields": {"workingDirectory": "/data"}}, "id": "email-finder-test", "name": "Test Email Finder", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-email-method", "leftValue": "={{ $json.email_method }}", "rightValue": "google_maps", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "email-method-switch", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [660, 300]}, {"parameters": {"command": "cd /app/google-maps-scraper && ./google-maps-scraper -q \"{{ $json.business_query }}\" -c {{ $json.results_count || 10 }} -email -depth 3 -json", "additionalFields": {"workingDirectory": "/app/google-maps-scraper"}}, "id": "google-maps-email-scraper", "name": "Google Maps with Email Extraction", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [860, 200]}, {"parameters": {"jsCode": "// Enhanced Email Finding - Multiple Methods\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const data = item.json;\n  \n  // Method 1: Google Maps results processing\n  if (data.places) {\n    for (const place of data.places) {\n      const businessData = {\n        business_name: place.name || 'Unknown Business',\n        address: place.address || '',\n        phone: place.phone || '',\n        website: place.website || '',\n        rating: place.rating || 0,\n        emails: place.emails || [],\n        email_sources: ['google_maps_direct'],\n        lead_score: 0\n      };\n      \n      // Calculate lead score\n      let score = 0;\n      if (businessData.emails && businessData.emails.length > 0) score += 40;\n      if (businessData.website) score += 20;\n      if (businessData.phone) score += 15;\n      if (businessData.rating >= 4.0) score += 15;\n      if (businessData.address) score += 10;\n      \n      businessData.lead_score = score;\n      \n      // If no emails found via Google Maps, prepare for web scraping\n      if (!businessData.emails || businessData.emails.length === 0) {\n        businessData.needs_email_search = true;\n        businessData.search_methods = ['web_scraping', 'pattern_generation'];\n      }\n      \n      results.push(businessData);\n    }\n  } else {\n    // Method 2: Manual business data processing\n    const businessData = {\n      business_name: data.business_name || data.company_name || 'Unknown Business',\n      address: data.address || '',\n      phone: data.phone || '',\n      website: data.website || data.url || '',\n      emails: data.emails || [],\n      email_sources: [],\n      needs_email_search: true,\n      search_methods: ['web_scraping', 'pattern_generation', 'osint'],\n      lead_score: 0\n    };\n    \n    results.push(businessData);\n  }\n}\n\nreturn results.map(item => ({ json: item }));"}, "id": "process-business-data", "name": "Process Business Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1060, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs-email-search", "leftValue": "={{ $json.needs_email_search }}", "rightValue": true, "operator": {"type": "boolean", "operation": "true"}}], "combinator": "and"}, "options": {}}, "id": "check-email-needed", "name": "Need Email Search?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1260, 200]}, {"parameters": {"pythonCode": "import json\nimport re\nimport requests\nfrom urllib.parse import urljoin, urlparse\n\n# Enhanced Email Finding with Multiple Methods\ndef find_emails_comprehensive(business_data):\n    \"\"\"\n    Comprehensive email finding using multiple sources\n    \"\"\"\n    business_name = business_data.get('business_name', '')\n    website = business_data.get('website', '')\n    emails_found = set(business_data.get('emails', []))\n    methods_used = list(business_data.get('email_sources', []))\n    \n    # Email regex pattern\n    email_pattern = re.compile(r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b')\n    \n    def extract_domain(url):\n        try:\n            parsed = urlparse(url)\n            return parsed.netloc.lower().replace('www.', '')\n        except:\n            return \"\"\n    \n    def is_valid_email(email):\n        if not email or '@' not in email:\n            return False\n        \n        # Filter out common false positives\n        false_positives = [\n            '.jpg', '.png', '.gif', '.pdf', '.doc', '.zip',\n            'example.com', 'domain.com', 'yoursite.com',\n            'noreply@', 'no-reply@', 'donotreply@'\n        ]\n        \n        email_lower = email.lower()\n        for fp in false_positives:\n            if fp in email_lower:\n                return False\n        \n        return True\n    \n    def generate_email_patterns(business_name, domain):\n        \"\"\"Generate common email patterns\"\"\"\n        patterns = []\n        domain = domain.lower()\n        \n        # Common business emails\n        common_prefixes = [\n            'info', 'contact', 'hello', 'sales', 'support', 'admin',\n            'marketing', 'business', 'office', 'team', 'help',\n            'inquiry', 'general', 'service', 'customerservice'\n        ]\n        \n        for prefix in common_prefixes:\n            patterns.append(f\"{prefix}@{domain}\")\n        \n        return patterns\n    \n    def scrape_website_simple(url):\n        \"\"\"Simple website scraping for emails\"\"\"\n        found_emails = set()\n        \n        if not url:\n            return found_emails\n        \n        # Ensure URL has protocol\n        if not url.startswith(('http://', 'https://')):\n            url = 'https://' + url\n        \n        try:\n            # Headers to avoid being blocked\n            headers = {\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'\n            }\n            \n            # Pages to check\n            pages_to_check = [\n                '', '/contact', '/about', '/contact-us', '/about-us',\n                '/team', '/staff', '/privacy', '/legal'\n            ]\n            \n            for page in pages_to_check:\n                try:\n                    page_url = urljoin(url, page)\n                    response = requests.get(page_url, headers=headers, timeout=10)\n                    \n                    if response.status_code == 200:\n                        # Extract emails from page content\n                        page_emails = email_pattern.findall(response.text)\n                        for email in page_emails:\n                            email = email.lower().strip()\n                            if is_valid_email(email):\n                                found_emails.add(email)\n                    \n                    # Don't overwhelm the server\n                    import time\n                    time.sleep(1)\n                    \n                except:\n                    continue\n                    \n        except Exception as e:\n            print(f\"Error scraping {url}: {e}\")\n        \n        return found_emails\n    \n    # Method 1: Web scraping if website available\n    if website and 'web_scraping' in business_data.get('search_methods', []):\n        try:\n            scraped_emails = scrape_website_simple(website)\n            emails_found.update(scraped_emails)\n            if scraped_emails:\n                methods_used.append(f'web_scraping ({len(scraped_emails)} found)')\n        except Exception as e:\n            print(f\"Web scraping failed: {e}\")\n    \n    # Method 2: Email pattern generation\n    if website and 'pattern_generation' in business_data.get('search_methods', []):\n        try:\n            domain = extract_domain(website)\n            if domain:\n                pattern_emails = generate_email_patterns(business_name, domain)\n                # Store patterns separately as they're not verified\n                business_data['email_patterns'] = pattern_emails\n                methods_used.append(f'pattern_generation ({len(pattern_emails)} generated)')\n        except Exception as e:\n            print(f\"Pattern generation failed: {e}\")\n    \n    # Update business data\n    business_data['emails'] = list(emails_found)\n    business_data['email_sources'] = methods_used\n    business_data['total_emails_found'] = len(emails_found)\n    \n    # Calculate enhanced lead score\n    score = business_data.get('lead_score', 0)\n    if emails_found:\n        score += len(emails_found) * 15  # 15 points per email found\n    if business_data.get('email_patterns'):\n        score += 10  # 10 points for having email patterns\n    \n    business_data['lead_score'] = min(score, 100)  # Cap at 100\n    business_data['email_search_complete'] = True\n    \n    return business_data\n\n# Process all input items\nresults = []\nfor item in _input.all():\n    business_data = item['json']\n    enhanced_data = find_emails_comprehensive(business_data)\n    results.append(enhanced_data)\n\nreturn results"}, "id": "enhanced-email-search", "name": "Enhanced Email Search", "type": "n8n-nodes-base.python", "typeVersion": 1, "position": [1460, 120]}, {"parameters": {"pythonCode": "# Alternative Email Finder using theHarvester approach\nimport subprocess\nimport json\nimport re\n\ndef osint_email_search(business_data):\n    \"\"\"\n    OSINT-based email search using multiple techniques\n    \"\"\"\n    business_name = business_data.get('business_name', '')\n    website = business_data.get('website', '')\n    existing_emails = set(business_data.get('emails', []))\n    \n    if not website:\n        return business_data\n    \n    # Extract domain\n    try:\n        from urllib.parse import urlparse\n        domain = urlparse(website).netloc.lower().replace('www.', '')\n    except:\n        return business_data\n    \n    # Method 1: DNS/WHOIS lookup (simple version)\n    def check_common_emails(domain):\n        common_emails = [\n            f'info@{domain}',\n            f'contact@{domain}',\n            f'hello@{domain}',\n            f'sales@{domain}',\n            f'support@{domain}',\n            f'admin@{domain}',\n            f'office@{domain}'\n        ]\n        return common_emails\n    \n    # Method 2: Search engine simulation (simplified)\n    def search_for_emails(business_name, domain):\n        # This would normally use search APIs or web scraping\n        # For now, return patterns based on business name\n        emails = []\n        \n        # Clean business name for email generation\n        clean_name = re.sub(r'[^a-zA-Z0-9]', '', business_name.lower())\n        if clean_name:\n            emails.extend([\n                f'{clean_name}@{domain}',\n                f'info@{domain}',\n                f'{clean_name}.info@{domain}'\n            ])\n        \n        return emails\n    \n    # Execute methods\n    osint_emails = set()\n    methods = []\n    \n    # Common email patterns\n    common_emails = check_common_emails(domain)\n    osint_emails.update(common_emails)\n    methods.append(f'common_patterns ({len(common_emails)} generated)')\n    \n    # Business-specific patterns\n    business_emails = search_for_emails(business_name, domain)\n    osint_emails.update(business_emails)\n    methods.append(f'business_patterns ({len(business_emails)} generated)')\n    \n    # Update business data\n    all_emails = existing_emails.union(osint_emails)\n    business_data['emails'] = list(all_emails)\n    business_data['osint_patterns'] = list(osint_emails)\n    business_data['osint_methods'] = methods\n    business_data['total_email_candidates'] = len(all_emails)\n    \n    return business_data\n\n# Process all input items\nresults = []\nfor item in _input.all():\n    business_data = item['json']\n    enhanced_data = osint_email_search(business_data)\n    results.append(enhanced_data)\n\nreturn results"}, "id": "osint-email-search", "name": "OSINT Email Search", "type": "n8n-nodes-base.python", "typeVersion": 1, "position": [1460, 280]}, {"parameters": {"command": "python3 /data/scripts/enhanced_email_finder.py --business \"{{ $json.business_name }}\" --website \"{{ $json.website }}\"", "additionalFields": {"workingDirectory": "/data"}}, "id": "web-scraper-email-search", "name": "Web Scraper Email Search", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [860, 400]}, {"parameters": {"jsCode": "// Merge and deduplicate email results from all sources\nconst inputData = $input.all();\nconst mergedResults = [];\n\n// Group results by business\nconst businessMap = new Map();\n\nfor (const item of inputData) {\n  const data = item.json;\n  const businessKey = data.business_name || 'unknown';\n  \n  if (!businessMap.has(businessKey)) {\n    businessMap.set(businessKey, {\n      business_name: data.business_name,\n      address: data.address || '',\n      phone: data.phone || '',\n      website: data.website || '',\n      rating: data.rating || 0,\n      emails: new Set(),\n      email_patterns: new Set(),\n      email_sources: new Set(),\n      lead_score: data.lead_score || 0,\n      methods_used: new Set()\n    });\n  }\n  \n  const business = businessMap.get(businessKey);\n  \n  // Merge emails\n  if (data.emails) {\n    data.emails.forEach(email => business.emails.add(email.toLowerCase().trim()));\n  }\n  \n  // Merge email patterns\n  if (data.email_patterns) {\n    data.email_patterns.forEach(pattern => business.email_patterns.add(pattern.toLowerCase().trim()));\n  }\n  \n  // Merge sources\n  if (data.email_sources) {\n    data.email_sources.forEach(source => business.email_sources.add(source));\n  }\n  \n  // Update lead score (take highest)\n  if (data.lead_score > business.lead_score) {\n    business.lead_score = data.lead_score;\n  }\n}\n\n// Convert sets back to arrays and calculate final scores\nfor (const [businessKey, business] of businessMap) {\n  const result = {\n    business_name: business.business_name,\n    address: business.address,\n    phone: business.phone,\n    website: business.website,\n    rating: business.rating,\n    emails: Array.from(business.emails),\n    email_patterns: Array.from(business.email_patterns),\n    email_sources: Array.from(business.email_sources),\n    total_emails_found: business.emails.size,\n    total_email_patterns: business.email_patterns.size,\n    lead_score: business.lead_score,\n    email_discovery_success: business.emails.size > 0,\n    has_email_patterns: business.email_patterns.size > 0,\n    discovery_timestamp: new Date().toISOString()\n  };\n  \n  // Enhanced lead scoring\n  let finalScore = result.lead_score;\n  \n  // Bonus points for email discovery\n  if (result.total_emails_found > 0) {\n    finalScore += result.total_emails_found * 20; // 20 points per verified email\n  }\n  \n  if (result.total_email_patterns > 0) {\n    finalScore += Math.min(result.total_email_patterns * 5, 25); // Max 25 points for patterns\n  }\n  \n  // Quality indicators\n  if (result.website && result.phone) finalScore += 15;\n  if (result.rating >= 4.5) finalScore += 10;\n  \n  result.final_lead_score = Math.min(finalScore, 100); // Cap at 100\n  \n  // Classify lead quality\n  if (result.final_lead_score >= 80) {\n    result.lead_quality = 'High';\n  } else if (result.final_lead_score >= 60) {\n    result.lead_quality = 'Medium';\n  } else if (result.final_lead_score >= 40) {\n    result.lead_quality = 'Low';\n  } else {\n    result.lead_quality = 'Poor';\n  }\n  \n  mergedResults.push(result);\n}\n\n// Sort by lead score (highest first)\nmergedResults.sort((a, b) => b.final_lead_score - a.final_lead_score);\n\nreturn mergedResults.map(item => ({ json: item }));"}, "id": "merge-email-results", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1660, 200]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "has-emails", "leftValue": "={{ $json.total_emails_found }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "filter-businesses-with-emails", "name": "Has Emails?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1860, 200]}, {"parameters": {"model": "llama3.1:latest", "options": {"baseURL": "http://localhost:11434"}, "prompt": "=Generate a personalized cold email for the following business:\n\nBusiness: {{ $json.business_name }}\nWebsite: {{ $json.website }}\nEmails: {{ $json.emails.join(', ') }}\nAddress: {{ $json.address }}\nPhone: {{ $json.phone }}\nRating: {{ $json.rating }}\nLead Score: {{ $json.final_lead_score }}\n\nCreate a professional, personalized cold email that:\n1. Shows you've researched their business\n2. Offers genuine value\n3. Has a clear call-to-action\n4. Keeps it concise (under 150 words)\n5. Uses a friendly but professional tone\n\nEmail subject line should be included.\nThe email should feel personal and not spammy.\n\nSubject: \nDear [Contact Name or Business Name],\n\n[Email body here]\n\nBest regards,\n[Your Name]"}, "id": "generate-personalized-email", "name": "Generate Personalized Email", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [2060, 120]}, {"parameters": {"jsCode": "// Format final results for output\nconst inputData = $input.all();\nconst finalResults = [];\n\nfor (const item of inputData) {\n  const data = item.json;\n  \n  // Extract AI-generated email content\n  let emailSubject = '';\n  let emailBody = '';\n  \n  if (data.message && data.message.content) {\n    const content = data.message.content;\n    \n    // Extract subject line\n    const subjectMatch = content.match(/Subject:\\s*(.+)/i);\n    if (subjectMatch) {\n      emailSubject = subjectMatch[1].trim();\n    }\n    \n    // Extract email body (everything after \"Dear\" until \"Best regards\")\n    const bodyMatch = content.match(/Dear[\\s\\S]*?(?=Best regards|Sincerely|Kind regards)/i);\n    if (bodyMatch) {\n      emailBody = bodyMatch[0].trim();\n    } else {\n      // Fallback: use entire content if pattern not found\n      emailBody = content;\n    }\n  }\n  \n  const result = {\n    // Business Information\n    business_name: data.business_name,\n    address: data.address,\n    phone: data.phone,\n    website: data.website,\n    rating: data.rating,\n    \n    // Email Discovery Results\n    emails_found: data.emails || [],\n    email_patterns: data.email_patterns || [],\n    primary_email: (data.emails && data.emails.length > 0) ? data.emails[0] : null,\n    total_emails: data.total_emails_found || 0,\n    email_sources: data.email_sources || [],\n    \n    // Lead Scoring\n    lead_score: data.final_lead_score || 0,\n    lead_quality: data.lead_quality || 'Unknown',\n    \n    // Generated Email Content\n    email_subject: emailSubject,\n    email_body: emailBody,\n    \n    // Metadata\n    discovery_timestamp: data.discovery_timestamp || new Date().toISOString(),\n    workflow_version: '2.0-opensource',\n    email_discovery_success: data.email_discovery_success || false,\n    \n    // Ready for sending\n    ready_for_outreach: !!(emailSubject && emailBody && data.emails && data.emails.length > 0)\n  };\n  \n  finalResults.push(result);\n}\n\n// Sort by lead score and readiness\nfinalResults.sort((a, b) => {\n  if (a.ready_for_outreach && !b.ready_for_outreach) return -1;\n  if (!a.ready_for_outreach && b.ready_for_outreach) return 1;\n  return b.lead_score - a.lead_score;\n});\n\nreturn finalResults.map(item => ({ json: item }));"}, "id": "format-final-results", "name": "Format Final Results", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2260, 120]}, {"parameters": {"operation": "appendOrUpdate", "documentId": "1wQs8kNqL5xP7yRvFt6J8K2mN9oP3qR4sT5uV6wX7yZ8a", "table": "Cold Email Results", "columns": {"mappingMode": "defineBelow", "value": {"Business Name": "={{ $json.business_name }}", "Website": "={{ $json.website }}", "Primary Email": "={{ $json.primary_email }}", "All Emails": "={{ $json.emails_found.join('; ') }}", "Phone": "={{ $json.phone }}", "Address": "={{ $json.address }}", "Lead Score": "={{ $json.lead_score }}", "Lead Quality": "={{ $json.lead_quality }}", "Email Subject": "={{ $json.email_subject }}", "Email Body": "={{ $json.email_body }}", "Ready for Outreach": "={{ $json.ready_for_outreach }}", "Discovery Date": "={{ $json.discovery_timestamp }}", "Email Sources": "={{ $json.email_sources.join('; ') }}"}}, "options": {}}, "id": "save-to-google-sheets", "name": "Save to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [2460, 120]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"status\": \"success\",\n  \"message\": \"Cold email workflow completed successfully\",\n  \"results_summary\": {\n    \"total_businesses_processed\": {{ $input.all().length }},\n    \"businesses_with_emails\": {{ $input.all().filter(item => item.json.emails_found && item.json.emails_found.length > 0).length }},\n    \"ready_for_outreach\": {{ $input.all().filter(item => item.json.ready_for_outreach).length }},\n    \"average_lead_score\": {{ Math.round($input.all().reduce((sum, item) => sum + (item.json.lead_score || 0), 0) / $input.all().length) }}\n  },\n  \"timestamp\": \"{{ new Date().toISOString() }}\",\n  \"workflow_version\": \"2.0-opensource-complete\"\n}"}, "id": "webhook-response", "name": "Success Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2660, 120]}, {"parameters": {"operation": "create", "base": {"__rl": true, "value": "appXXXXXXXXXXXXXX", "mode": "list", "cachedResultName": "Cold Email Database"}, "table": {"__rl": true, "value": "tblYYYYYYYYYYYYYY", "mode": "list", "cachedResultName": "Lead Results"}, "columns": {"mappingMode": "defineBelow", "value": {"Business Name": "={{ $json.business_name }}", "Primary Email": "={{ $json.primary_email }}", "Website": "={{ $json.website }}", "Lead Score": "={{ $json.lead_score }}", "Email Content": "={{ $json.email_body }}", "Status": "Ready for Outreach"}}, "options": {}}, "id": "save-to-airtable", "name": "Save to Airtable (Optional)", "type": "n8n-nodes-base.airtable", "typeVersion": 1, "position": [2460, 220]}], "connections": {"Start Workflow": {"main": [[{"node": "Test Email Finder", "type": "main", "index": 0}]]}, "Test Email Finder": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Choose Email Method": {"main": [[{"node": "Google Maps with Email Extraction", "type": "main", "index": 0}], [{"node": "Web Scraper Email Search", "type": "main", "index": 0}]]}, "Google Maps with Email Extraction": {"main": [[{"node": "Process Business Data", "type": "main", "index": 0}]]}, "Process Business Data": {"main": [[{"node": "Need Email Search?", "type": "main", "index": 0}]]}, "Need Email Search?": {"main": [[{"node": "Enhanced Email Search", "type": "main", "index": 0}, {"node": "OSINT Email Search", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Enhanced Email Search": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "OSINT Email Search": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Web Scraper Email Search": {"main": [[{"node": "Process Business Data", "type": "main", "index": 0}]]}, "Merge Email Results": {"main": [[{"node": "Has Emails?", "type": "main", "index": 0}]]}, "Has Emails?": {"main": [[{"node": "Generate Personalized Email", "type": "main", "index": 0}]]}, "Generate Personalized Email": {"main": [[{"node": "Format Final Results", "type": "main", "index": 0}]]}, "Format Final Results": {"main": [[{"node": "Save to Google Sheets", "type": "main", "index": 0}, {"node": "Save to Airtable (Optional)", "type": "main", "index": 0}]]}, "Save to Google Sheets": {"main": [[{"node": "Success Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2024-01-20T10:00:00.000Z", "versionId": "opensource-email-v2"}