"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PUPPETEER_REVISIONS = void 0;
/**
 * @internal
 */
exports.PUPPETEER_REVISIONS = Object.freeze({
    chrome: '139.0.7258.66',
    'chrome-headless-shell': '139.0.7258.66',
    firefox: 'stable_141.0.2',
});
//# sourceMappingURL=revisions.js.map