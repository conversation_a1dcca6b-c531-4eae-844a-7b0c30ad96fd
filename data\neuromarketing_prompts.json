{"psychology_triggers": {"reciprocity": {"description": "Offering value first to encourage positive response", "prompts": ["I'd like to share some valuable market insights with you...", "I've prepared a complimentary market analysis for your area...", "Here's an exclusive report that might benefit your clients...", "I have some information that could be helpful for your business..."], "effectiveness_score": 8.2, "best_personas": ["first_time_buyer", "empty_nester"]}, "scarcity": {"description": "Highlighting limited availability to create urgency", "prompts": ["Only 3 luxury properties available in this price range...", "This off-market opportunity won't last long...", "With inventory at historic lows, timing is critical...", "Properties like this typically sell within 48 hours..."], "effectiveness_score": 9.1, "best_personas": ["luxury_buyer", "tech_professional"]}, "social_proof": {"description": "Using others' success to validate the opportunity", "prompts": ["My clients consistently achieve 15% above asking price...", "Similar properties in your area sold within 48 hours...", "Top 1% agent in luxury home sales for three consecutive years...", "Other agents in your market have found this approach very successful..."], "effectiveness_score": 8.9, "best_personas": ["luxury_buyer", "first_time_buyer", "empty_nester"]}, "loss_aversion": {"description": "Emphasizing potential missed opportunities", "prompts": ["Every month of delay costs approximately $12,000 in potential equity...", "Properties like this typically appreciate 8% annually...", "Missing this market cycle could cost you significantly...", "The opportunity cost of waiting in this market is substantial..."], "effectiveness_score": 9.3, "best_personas": ["investor", "luxury_buyer"]}, "anchoring": {"description": "Using specific numbers to set value expectations", "prompts": ["Similar to the $9.2M property that just sold on Rodeo Drive...", "Based on the recent $2.3M sale in your neighborhood...", "Properties in this category average $847K in your market...", "The last comparable sale was $1.8M just 30 days ago..."], "effectiveness_score": 8.8, "best_personas": ["luxury_buyer", "investor", "tech_professional"]}}, "buyer_persona_templates": {"luxury_buyer": {"communication_style": "professional_sophisticated", "key_motivators": ["exclusivity", "prestige", "investment_value"], "optimal_triggers": ["scarcity", "anchoring", "social_proof"], "avoid_triggers": ["heavy_reciprocity"], "tone": "refined, data-driven, exclusive", "personalization_focus": ["recent_luxury_sales", "market_expertise", "exclusive_access"]}, "investor": {"communication_style": "analytical_roi_focused", "key_motivators": ["cash_flow", "appreciation", "tax_benefits"], "optimal_triggers": ["loss_aversion", "anchoring"], "avoid_triggers": ["emotional_appeals"], "tone": "numbers-heavy, opportunity-focused, urgent", "personalization_focus": ["roi_calculations", "market_timing", "investment_track_record"]}, "first_time_buyer": {"communication_style": "educational_supportive", "key_motivators": ["guidance", "security", "value"], "optimal_triggers": ["reciprocity", "social_proof"], "avoid_triggers": ["high_pressure", "complex_jargon"], "tone": "warm, educational, reassuring", "personalization_focus": ["success_stories", "step_by_step_guidance", "local_expertise"]}, "empty_nester": {"communication_style": "personal_lifestyle_focused", "key_motivators": ["convenience", "lifestyle", "simplicity"], "optimal_triggers": ["reciprocity", "social_proof"], "avoid_triggers": ["urgency_pressure"], "tone": "warm, personal, understanding", "personalization_focus": ["lifestyle_benefits", "peer_success_stories", "convenience_factors"]}, "tech_professional": {"communication_style": "efficient_data_driven", "key_motivators": ["efficiency", "innovation", "value_optimization"], "optimal_triggers": ["scarcity", "anchoring"], "avoid_triggers": ["lengthy_explanations"], "tone": "precise, technology-forward, efficient", "personalization_focus": ["market_analytics", "timing_optimization", "tech_amenities"]}}, "personalization_elements": {"agent_specific": ["recent_sales_data", "specialization_area", "years_experience", "market_expertise", "client_testimonials", "professional_achievements"], "market_specific": ["local_market_trends", "recent_comparable_sales", "inventory_levels", "price_appreciation", "seasonal_factors", "neighborhood_insights"], "buyer_specific": ["budget_range", "property_preferences", "timeline_requirements", "motivation_factors", "decision_criteria", "communication_preferences"]}, "compliance_templates": {"unsubscribe_footer": "To unsubscribe from future communications, please reply with 'UNSUBSCRIBE' or click here: {{unsubscribe_link}}", "sender_identification": "{{sender_name}}\n{{title}}\n{{company_name}}\n{{license_number}}\n{{physical_address}}", "privacy_notice": "Your privacy is important to us. We handle your information in accordance with our privacy policy.", "fair_housing": "We are committed to fair housing practices and equal opportunity."}}