#!/usr/bin/env python3
"""
Test Suite for Open-Source Email Finding System
Validates all email discovery methods
"""

import requests
import json
import time
import subprocess
import os

def test_google_maps_email_extraction():
    """Test Google Maps built-in email extraction"""
    print("🧪 Testing Google Maps Email Extraction...")
    
    try:
        # Test query
        query = "coffee shops in Seattle"
        
        # Run Google Maps scraper with email extraction using input file method
        result = subprocess.run([
            'docker', 'exec', 'mysetupn8n-gosom-scraper-1', 
            'bash', '-c',
            f'echo "{query}" > /tmp/query.txt && google-maps-scraper -input /tmp/query.txt -c 3 -email -depth 2 -json'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            try:
                data = json.loads(result.stdout)
                emails_found = 0
                for business in data:
                    if business.get('emails'):
                        emails_found += len(business['emails'])
                
                print(f"✅ Google Maps Test: {len(data)} businesses, {emails_found} emails found")
                return True
            except json.JSONDecodeError:
                print(f"❌ Google Maps Test: Invalid JSON response")
                return False
        else:
            print(f"❌ Google Maps Test: Command failed - {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Google Maps Test: {e}")
        return False

def test_enhanced_email_finder():
    """Test enhanced email finder script"""
    print("🧪 Testing Enhanced Email Finder...")
    
    try:
        # Test the enhanced email finder
        result = subprocess.run([
            'docker', 'exec', 'mysetupn8n-n8n-1',
            'python3', '/data/scripts/enhanced_email_finder.py'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ Enhanced Email Finder: Script executed successfully")
            print(f"📝 Output preview: {result.stdout[:200]}...")
            return True
        else:
            print(f"❌ Enhanced Email Finder: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Enhanced Email Finder: {e}")
        return False

def test_n8n_workflow():
    """Test N8N workflow endpoint"""
    print("🧪 Testing N8N Workflow...")
    
    try:
        # Test webhook endpoint
        webhook_url = "http://localhost:5678/webhook/start-cold-email"
        
        test_data = {
            "business_query": "test coffee shop",
            "results_count": 2,
            "email_method": "google_maps"
        }
        
        response = requests.post(webhook_url, json=test_data, timeout=30)
        
        if response.status_code == 200:
            print("✅ N8N Workflow: Webhook responded successfully")
            return True
        else:
            print(f"❌ N8N Workflow: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ N8N Workflow: Connection error - {e}")
        return False

def test_ollama_ai():
    """Test Ollama AI service"""
    print("🧪 Testing Ollama AI...")
    
    try:
        # Test Ollama API
        ollama_url = "http://localhost:11434/api/generate"
        
        test_prompt = {
            "model": "llama3.1:latest",
            "prompt": "Write a short test email for a coffee shop.",
            "stream": False
        }
        
        response = requests.post(ollama_url, json=test_prompt, timeout=30)
        
        if response.status_code == 200:
            print("✅ Ollama AI: Model responding successfully")
            return True
        else:
            print(f"❌ Ollama AI: HTTP {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Ollama AI: Connection error - {e}")
        return False

def test_all_services():
    """Test all required services are running"""
    print("🧪 Testing Service Availability...")
    
    services = {
        "N8N": ("http://localhost:5678", 200),
        "Ollama": ("http://localhost:11434", 200),
        "PostgreSQL": ("localhost", 5432),  # TCP port check
        "Google Maps Scraper": ("http://localhost:3007", 200)
    }
    
    results = {}
    
    for service_name, (url, expected) in services.items():
        try:
            if service_name == "PostgreSQL":
                # TCP port check for PostgreSQL
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(5)
                result = sock.connect_ex((url, expected))
                sock.close()
                
                if result == 0:
                    print(f"✅ {service_name}: Port {expected} is open")
                    results[service_name] = True
                else:
                    print(f"❌ {service_name}: Port {expected} is closed")
                    results[service_name] = False
            else:
                # HTTP check
                response = requests.get(url, timeout=10)
                if response.status_code == expected:
                    print(f"✅ {service_name}: Service responding")
                    results[service_name] = True
                else:
                    print(f"❌ {service_name}: HTTP {response.status_code}")
                    results[service_name] = False
                    
        except Exception as e:
            print(f"❌ {service_name}: {e}")
            results[service_name] = False
    
    return results

def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🚀 COMPREHENSIVE EMAIL FINDING SYSTEM TEST")
    print("=" * 60)
    print()
    
    # Test results
    test_results = {}
    
    # 1. Test service availability
    print("1️⃣ SERVICE AVAILABILITY TESTS")
    print("-" * 30)
    service_results = test_all_services()
    test_results['services'] = service_results
    print()
    
    # 2. Test Ollama AI
    print("2️⃣ AI SERVICE TESTS")
    print("-" * 30)
    test_results['ollama'] = test_ollama_ai()
    print()
    
    # 3. Test Google Maps email extraction
    print("3️⃣ EMAIL EXTRACTION TESTS")
    print("-" * 30)
    test_results['google_maps'] = test_google_maps_email_extraction()
    print()
    
    # 4. Test enhanced email finder
    print("4️⃣ ENHANCED FINDER TESTS")
    print("-" * 30)
    test_results['enhanced_finder'] = test_enhanced_email_finder()
    print()
    
    # 5. Test N8N workflow
    print("5️⃣ WORKFLOW INTEGRATION TESTS")
    print("-" * 30)
    test_results['n8n_workflow'] = test_n8n_workflow()
    print()
    
    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in test_results.items():
        if isinstance(results, dict):
            for test_name, result in results.items():
                total_tests += 1
                if result:
                    passed_tests += 1
                    print(f"✅ {category.upper()} - {test_name}: PASS")
                else:
                    print(f"❌ {category.upper()} - {test_name}: FAIL")
        else:
            total_tests += 1
            if results:
                passed_tests += 1
                print(f"✅ {category.upper()}: PASS")
            else:
                print(f"❌ {category.upper()}: FAIL")
    
    print()
    print(f"📈 OVERALL RESULT: {passed_tests}/{total_tests} tests passed ({(passed_tests/total_tests*100):.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 ALL SYSTEMS OPERATIONAL! Ready for production use.")
    elif passed_tests >= total_tests * 0.8:
        print("⚠️  Most systems operational. Check failed tests.")
    else:
        print("🚨 Multiple system failures. Please check configuration.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_comprehensive_test()
    exit(0 if success else 1)
