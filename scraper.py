#!/opt/venv/bin/python3
"""
Open-source web scraper using Selenium
Extracts email addresses and company information from websites
"""

import sys
import json
import re
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

def extract_emails(text):
    """Extract email addresses from text"""
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    return list(set(re.findall(email_pattern, text)))

def scrape_website(url):
    """Scrape website for contact information"""
    
    # Set up Chrome options
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
    
    driver = None
    try:
        # Initialize the driver
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        
        # Navigate to the website
        driver.get(url)
        
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Get page content
        text_content = driver.find_element(By.TAG_NAME, "body").text
        
        # Extract emails from main page
        emails = extract_emails(text_content)
        
        # Look for contact/about links
        additional_emails = []
        try:
            contact_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'contact') or contains(@href, 'about') or contains(text(), 'Contact') or contains(text(), 'About')]")
            
            for link in contact_links[:2]:  # Check first 2 contact/about pages
                try:
                    href = link.get_attribute('href')
                    if href and href != url:
                        if href.startswith('/'):
                            href = url.rstrip('/') + href
                        elif not href.startswith('http'):
                            href = url.rstrip('/') + '/' + href
                        
                        driver.get(href)
                        time.sleep(2)  # Brief pause
                        contact_text = driver.find_element(By.TAG_NAME, "body").text
                        additional_emails.extend(extract_emails(contact_text))
                        
                        # Go back to main page
                        driver.get(url)
                        time.sleep(1)
                except:
                    continue
        except:
            pass
        
        # Combine all emails
        all_emails = list(set(emails + additional_emails))
        
        # Extract company description
        description = ""
        try:
            # Try meta description first
            meta_desc = driver.find_elements(By.XPATH, "//meta[@name='description']")
            if meta_desc:
                description = meta_desc[0].get_attribute('content')
            
            # If no meta description, try headings
            if not description or len(description) < 20:
                headings = driver.find_elements(By.XPATH, "//h1 | //h2 | //h3")
                for heading in headings[:3]:
                    text = heading.text.strip()
                    if text and len(text) > 20:
                        description = text[:200] + "..." if len(text) > 200 else text
                        break
        except:
            pass
        
        return {
            "emails": all_emails,
            "primary_email": all_emails[0] if all_emails else "",
            "description": description,
            "url": url,
            "status": "scraped"
        }
        
    except TimeoutException:
        return {
            "emails": [],
            "primary_email": "",
            "description": "",
            "url": url,
            "error": "Page load timeout",
            "status": "timeout"
        }
    except WebDriverException as e:
        return {
            "emails": [],
            "primary_email": "",
            "description": "",
            "url": url,
            "error": f"WebDriver error: {str(e)}",
            "status": "error"
        }
    except Exception as e:
        return {
            "emails": [],
            "primary_email": "",
            "description": "",
            "url": url,
            "error": str(e),
            "status": "error"
        }
    finally:
        if driver:
            driver.quit()

def main():
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: python3 scraper.py <url>"}))
        sys.exit(1)
    
    url = sys.argv[1]
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    result = scrape_website(url)
    print(json.dumps(result))

if __name__ == "__main__":
    main()
