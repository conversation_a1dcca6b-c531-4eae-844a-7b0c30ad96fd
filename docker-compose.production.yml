version: '3.8'

services:
  # Enhanced n8n with production optimizations
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n_production
    restart: unless-stopped
    environment:
      # Database Configuration
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_USER=n8n_user
      - DB_POSTGRESDB_PASSWORD=${POSTGRES_PASSWORD}
      
      # Security Configuration
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=${N8N_USER}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_PASSWORD}
      - N8N_ENCRYPTION_KEY=${ENCRYPTION_KEY}
      
      # Webhook Configuration
      - WEBHOOK_URL=https://your-domain.com/webhook/
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      
      # Performance Optimization
      - NODE_ENV=production
      - EXECUTIONS_DATA_PRUNE=true
      - EXECUTIONS_DATA_MAX_AGE=168
      - N8N_METRICS=true
      
      # Email Configuration
      - N8N_EMAIL_MODE=smtp
      - N8N_SMTP_HOST=${SMTP_HOST}
      - N8N_SMTP_PORT=${SMTP_PORT}
      - N8N_SMTP_USER=${SMTP_USER}
      - N8N_SMTP_PASS=${SMTP_PASS}
      - N8N_SMTP_SSL=${SMTP_SSL}
      
      # Community Nodes
      - N8N_COMMUNITY_PACKAGES_ENABLED=true
      
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./data:/data
      - ./scripts:/app/scripts:ro
    ports:
      - "5678:5678"
    depends_on:
      - postgres
      - redis
      - ollama
    networks:
      - n8n_network
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'

  # Production PostgreSQL with optimizations
  postgres:
    image: postgres:15
    container_name: postgres_production
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    ports:
      - "5432:5432"
    networks:
      - n8n_network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n_user -d n8n"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: redis_production
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - n8n_network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Ollama with GPU support
  ollama:
    image: ollama/ollama:latest
    container_name: ollama_production
    restart: unless-stopped
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
      - OLLAMA_MODELS=/root/.ollama/models
    volumes:
      - ollama_data:/root/.ollama
    ports:
      - "11434:11434"
    networks:
      - n8n_network
    deploy:
      resources:
        limits:
          memory: 16G
          cpus: '4.0'
        reservations:
          memory: 8G
          cpus: '2.0'
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  # Nginx reverse proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: nginx_production
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - n8n
    networks:
      - n8n_network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus_production
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - n8n_network

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: grafana_production
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - n8n_network

  # Email deliverability monitoring
  postfix:
    image: juanluisbaptiste/postfix:latest
    container_name: postfix_production
    restart: unless-stopped
    environment:
      - SMTP_SERVER=${SMTP_RELAY_HOST}
      - SMTP_USERNAME=${SMTP_RELAY_USER}
      - SMTP_PASSWORD=${SMTP_RELAY_PASS}
      - SERVER_HOSTNAME=${DOMAIN_NAME}
    volumes:
      - ./postfix/main.cf:/etc/postfix/main.cf:ro
    ports:
      - "25:25"
      - "587:587"
    networks:
      - n8n_network

volumes:
  n8n_data:
    driver: local
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ollama_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  n8n_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
