#!/bin/bash

# Google Maps Email Extraction Enabler
# This script demonstrates how to use the built-in email extraction in the Google Maps scraper

echo "🔍 Google Maps Email Extraction Tool"
echo "=================================="

# Check if we have parameters
if [ $# -eq 0 ]; then
    echo "Usage: $0 \"search query\" [number_of_results]"
    echo "Example: $0 \"restaurants in New York\" 10"
    echo ""
    echo "Testing with default query..."
    QUERY="coffee shops in Seattle"
    COUNT=5
else
    QUERY="$1"
    COUNT="${2:-10}"
fi

echo "Search Query: $QUERY"
echo "Results Count: $COUNT"
echo ""

# Navigate to Google Maps scraper directory
cd /app/google-maps-scraper 2>/dev/null || {
    echo "❌ Google Maps scraper not found. Running from Docker..."
    
    # Run via Docker
    docker exec -it gosom-scraper bash -c "
        echo '🚀 Running Google Maps scraper with email extraction...'
        google-maps-scraper -q \"$QUERY\" -c $COUNT -email -depth 3 -json > /tmp/gmaps_results.json
        
        echo '📊 Processing results...'
        
        # Parse and display results
        python3 -c \"
import json
import sys

try:
    with open('/tmp/gmaps_results.json', 'r') as f:
        data = json.load(f)
    
    print(f'\\n✅ Found {len(data)} businesses')
    print('=' * 50)
    
    for i, business in enumerate(data, 1):
        print(f'\\n{i}. {business.get(\\\"name\\\", \\\"Unknown Business\\\")}')
        print(f'   Website: {business.get(\\\"website\\\", \\\"N/A\\\")}')
        print(f'   Phone: {business.get(\\\"phone\\\", \\\"N/A\\\")}')
        
        emails = business.get('emails', [])
        if emails:
            print(f'   📧 Emails Found: {\\\", \\\".join(emails)}')
        else:
            print(f'   📧 Emails: None found')
        
        print(f'   Rating: {business.get(\\\"rating\\\", \\\"N/A\\\")}')
        print(f'   Address: {business.get(\\\"address\\\", \\\"N/A\\\")}')
        
        if i >= 5:  # Limit display to first 5
            remaining = len(data) - 5
            if remaining > 0:
                print(f'\\n... and {remaining} more businesses')
            break
            
except Exception as e:
    print(f'❌ Error processing results: {e}')
\"
        
        echo ''
        echo '💾 Full results saved to /tmp/gmaps_results.json'
        echo '🎯 Email extraction complete!'
    "
    
    exit 0
fi

# If we're running locally
echo "🚀 Running Google Maps scraper with email extraction..."

# Run the scraper with email extraction enabled
./google-maps-scraper -q "$QUERY" -c "$COUNT" -email -depth 3 -json > results_with_emails.json

echo "📊 Processing results..."

# Parse and display results using Python
python3 << EOF
import json
import sys

try:
    with open('results_with_emails.json', 'r') as f:
        data = json.load(f)
    
    print(f'\n✅ Found {len(data)} businesses')
    print('=' * 50)
    
    total_emails = 0
    businesses_with_emails = 0
    
    for i, business in enumerate(data, 1):
        print(f'\n{i}. {business.get("name", "Unknown Business")}')
        print(f'   Website: {business.get("website", "N/A")}')
        print(f'   Phone: {business.get("phone", "N/A")}')
        
        emails = business.get('emails', [])
        if emails:
            print(f'   📧 Emails Found: {", ".join(emails)}')
            total_emails += len(emails)
            businesses_with_emails += 1
        else:
            print(f'   📧 Emails: None found')
        
        print(f'   Rating: {business.get("rating", "N/A")}')
        print(f'   Address: {business.get("address", "N/A")}')
        
        if i >= 5:  # Limit display to first 5
            remaining = len(data) - 5
            if remaining > 0:
                print(f'\n... and {remaining} more businesses')
            break
    
    print(f'\n📈 EMAIL DISCOVERY SUMMARY:')
    print(f'   Total Businesses: {len(data)}')
    print(f'   Businesses with Emails: {businesses_with_emails}')
    print(f'   Total Emails Found: {total_emails}')
    print(f'   Success Rate: {(businesses_with_emails/len(data)*100):.1f}%')
            
except Exception as e:
    print(f'❌ Error processing results: {e}')
EOF

echo ""
echo "💾 Full results saved to results_with_emails.json"
echo "🎯 Email extraction complete!"
echo ""
echo "🚀 To use this data in N8N workflow:"
echo "   1. Copy results_with_emails.json to ./data/ folder"
echo "   2. Import workflow-cold-email-complete-opensource.json"
echo "   3. Run the workflow with email_method: 'google_maps'"
