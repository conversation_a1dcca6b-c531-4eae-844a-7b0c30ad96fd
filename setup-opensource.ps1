# Open Source Cold Email Automation Setup Script (Windows)
Write-Host "🚀 Setting up Open Source Cold Email Automation Stack..." -ForegroundColor Green

# Create data directory if it doesn't exist
if (!(Test-Path -Path "data")) {
    New-Item -ItemType Directory -Path "data"
    Write-Host "📁 Created data directory" -ForegroundColor Yellow
}

# Build and start all services
Write-Host "📦 Building custom N8N image with dependencies..." -ForegroundColor Cyan
docker compose build n8n

Write-Host "🏃 Starting all services..." -ForegroundColor Cyan
docker compose up -d

# Wait for services to start
Write-Host "⏳ Waiting for services to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Install and start Ollama model
Write-Host "🤖 Setting up Ollama AI model..." -ForegroundColor Magenta
docker exec mysetupn8n-ollama-1 ollama pull llama3.1:8b

# Check service status
Write-Host "✅ Checking service status..." -ForegroundColor Green
docker compose ps

Write-Host ""
Write-Host "🎉 Setup Complete! Your open-source automation stack is ready!" -ForegroundColor Green
Write-Host ""
Write-Host "📊 Available Services:" -ForegroundColor White
Write-Host "   • N8N Workflow: http://localhost:5678" -ForegroundColor Cyan
Write-Host "   • Mailtrain: http://localhost:3000 (admin/admin123)" -ForegroundColor Cyan
Write-Host "   • Google Maps Scraper: http://localhost:3007" -ForegroundColor Cyan
Write-Host "   • Ollama AI: http://localhost:11434" -ForegroundColor Cyan
Write-Host "   • File Server: http://localhost:8080" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔧 Next Steps:" -ForegroundColor Yellow
Write-Host "   1. Import workflow-cold-email-opensource.json into N8N" -ForegroundColor White
Write-Host "   2. Configure SMTP settings for email sending" -ForegroundColor White
Write-Host "   3. Set up Hunter.io free API key (100 requests/month)" -ForegroundColor White
Write-Host "   4. Test the workflow with a sample query" -ForegroundColor White
Write-Host ""
Write-Host "📁 Data Location: ./data/leads.csv" -ForegroundColor Gray
Write-Host "🔍 Scraper Script: ./scraper.py" -ForegroundColor Gray
