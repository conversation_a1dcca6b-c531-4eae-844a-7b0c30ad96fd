#!/bin/bash

# Open Source Cold Email Automation Setup Script
echo "🚀 Setting up Open Source Cold Email Automation Stack..."

# Create data directory if it doesn't exist
mkdir -p data

# Build and start all services
echo "📦 Building custom N8N image with dependencies..."
docker compose build n8n

echo "🏃 Starting all services..."
docker compose up -d

# Wait for services to start
echo "⏳ Waiting for services to initialize..."
sleep 30

# Install and start Ollama model
echo "🤖 Setting up Ollama AI model..."
docker exec mysetupn8n-ollama-1 ollama pull llama3.1:8b

# Check service status
echo "✅ Checking service status..."
docker compose ps

echo ""
echo "🎉 Setup Complete! Your open-source automation stack is ready!"
echo ""
echo "📊 Available Services:"
echo "   • N8N Workflow: http://localhost:5678"
echo "   • Mailtrain: http://localhost:3000 (admin/admin123)"
echo "   • Google Maps Scraper: http://localhost:3007"
echo "   • Ollama AI: http://localhost:11434"
echo "   • File Server: http://localhost:8080"
echo ""
echo "🔧 Next Steps:"
echo "   1. Import workflow-cold-email-opensource.json into N8N"
echo "   2. Configure SMTP settings for email sending"
echo "   3. Set up Hunter.io free API key (100 requests/month)"
echo "   4. Test the workflow with a sample query"
echo ""
echo "📁 Data Location: ./data/leads.csv"
echo "🔍 Scraper Script: ./scraper.py"
