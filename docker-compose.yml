version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    restart: unless-stopped
    ports:
      - "27017:27017"
    networks:
      - mailnet
      
  redis:
    image: redis:7.2
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - mailnet
      
  n8n:
    build:
      context: .
      dockerfile: Dockerfile.n8n
    environment:
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - DB_TYPE=postgresdb
      - DB_POSTGRESDB_DATABASE=n8n
      - DB_POSTGRESDB_HOST=postgres
      - DB_POSTGRESDB_PORT=5432
      - DB_POSTGRESDB_USER=n8n
      - DB_POSTGRESDB_PASSWORD=n8npass
      - NODE_ENV=production
      - WEBHOOK_TUNNEL_URL=http://localhost:5678/
    ports:
      - "5678:5678"
    depends_on:
      - postgres
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./data:/data
      - ./scraper.py:/app/scraper.py:ro
      - ./enhanced_email_finder.py:/data/scripts/enhanced_email_finder.py:ro
    networks:
      - mailnet

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=n8npass
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - mailnet

  gosom-scraper:
    image: gosom/google-maps-scraper:latest
    ports:
      - "3007:3000"
    networks:
      - mailnet

  mysql:
    image: mysql:5.7
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=rootpass
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - d:/Automation/My Setup N8N/init-mailtrain.sql:/docker-entrypoint-initdb.d/init-mailtrain.sql:ro
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-prootpass"]
      interval: 10s
      timeout: 5s
      retries: 10
    networks:
      - mailnet

  mailtrain:
    image: mailtrain/mailtrain:latest
    restart: unless-stopped
    ports:
      - "3000:3000"  # Trusted endpoint
      - "3003:3003"  # Sandbox endpoint
      - "3004:3004"  # Public endpoint
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_DATABASE=mailtrain
      - MYSQL_USER=mailtrain
      - MYSQL_PASSWORD=mailpass
      - MONGO_HOST=mongodb
      - REDIS_HOST=redis
      - ADMIN_PASSWORD=admin123
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - mailnet

  # Open-source AI service
  ollama:
    image: ollama/ollama:latest
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - mailnet
    environment:
      - OLLAMA_HOST=0.0.0.0

  # File storage for CSV data
  fileserver:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./data:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - mailnet

volumes:
  n8n_data:
  pgdata:
  mysql_data:
  ollama_data:

networks:
  mailnet:
    driver: bridge
