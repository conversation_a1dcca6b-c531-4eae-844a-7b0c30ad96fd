{
  "name": "Complete Open-Source Cold Email with Gmail Drafts",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "start-cold-email",
        "options": {
          "responseMode": "responseNode"
        }
      },
      "id": "webhook-trigger",
      "name": "🚀 Start Cold Email Workflow",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [140, 300],
      "webhookId": "cold-email-start"
    },
    {
      "parameters": {
        "jsCode": "// Process input and prepare for Google Maps scraping\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const data = item.json;\n  \n  // Validate required inputs\n  if (!data.business_query) {\n    throw new Error('business_query is required');\n  }\n  \n  const processedData = {\n    business_query: data.business_query,\n    results_count: data.results_count || 20,\n    email_method: data.email_method || 'google_maps_with_scraping',\n    target_location: data.target_location || '',\n    industry_filter: data.industry_filter || '',\n    approval_required: true, // Always require approval\n    workflow_id: `workflow_${Date.now()}`,\n    status: 'started',\n    timestamp: new Date().toISOString()\n  };\n  \n  results.push(processedData);\n}\n\nreturn results.map(item => ({ json: item }));"
      },
      "id": "process-input",
      "name": "📝 Process Input & Validate",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [360, 300]
    },
    {
      "parameters": {
        "command": "cd /app && echo \"{{ $json.business_query }}\" > /tmp/gmaps_query.txt && google-maps-scraper -input /tmp/gmaps_query.txt -c {{ $json.results_count }} -email -depth 3 -json",
        "additionalFields": {
          "workingDirectory": "/app"
        }
      },
      "id": "google-maps-scraper",
      "name": "🗺️ Google Maps with Email Extraction",
      "type": "n8n-nodes-base.executeCommand",
      "typeVersion": 1,
      "position": [580, 300]
    },
    {
      "parameters": {
        "jsCode": "// Parse Google Maps results and extract business data with emails\nconst inputData = $input.all();\nconst results = [];\n\nfor (const item of inputData) {\n  const stdout = item.json.stdout;\n  \n  if (!stdout) {\n    console.log('No stdout from Google Maps scraper');\n    continue;\n  }\n  \n  // Parse JSON lines from Google Maps scraper output\n  const lines = stdout.split('\\n').filter(line => line.trim());\n  \n  for (const line of lines) {\n    try {\n      // Skip non-JSON lines (like headers)\n      if (!line.trim().startsWith('{')) continue;\n      \n      const business = JSON.parse(line);\n      \n      // Only process businesses with basic required data\n      if (!business.title) continue;\n      \n      const businessData = {\n        // Core business information\n        business_name: business.title || 'Unknown Business',\n        address: business.address || '',\n        phone: business.phone || '',\n        website: business.web_site || business.website || '',\n        rating: business.review_rating || 0,\n        review_count: business.review_count || 0,\n        \n        // Email information\n        emails_found: business.emails || [],\n        email_sources: business.emails ? ['google_maps_direct'] : [],\n        \n        // Additional business context\n        business_type: business.type || '',\n        hours: business.hours || '',\n        price_level: business.price_level || '',\n        \n        // Lead scoring\n        lead_score: 0,\n        lead_quality: 'Unknown',\n        \n        // Workflow tracking\n        needs_additional_email_search: (!business.emails || business.emails.length === 0),\n        workflow_status: 'scraped',\n        scraped_at: new Date().toISOString()\n      };\n      \n      // Calculate initial lead score\n      let score = 0;\n      if (businessData.emails_found.length > 0) score += 40;\n      if (businessData.website) score += 20;\n      if (businessData.phone) score += 15;\n      if (businessData.rating >= 4.0) score += 15;\n      if (businessData.review_count >= 50) score += 10;\n      \n      businessData.lead_score = score;\n      \n      // Determine lead quality\n      if (score >= 80) businessData.lead_quality = 'High';\n      else if (score >= 60) businessData.lead_quality = 'Medium';\n      else if (score >= 40) businessData.lead_quality = 'Low';\n      else businessData.lead_quality = 'Poor';\n      \n      results.push(businessData);\n      \n    } catch (e) {\n      console.log(`Error parsing line: ${line.substring(0, 100)}... Error: ${e.message}`);\n      continue;\n    }\n  }\n}\n\nconsole.log(`Processed ${results.length} businesses from Google Maps`);\nreturn results.map(item => ({ json: item }));"
      },
      "id": "parse-gmaps-results",
      "name": "🔍 Parse Business Results",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [800, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "needs-email-search",
              "leftValue": "={{ $json.needs_additional_email_search }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "true"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "check-needs-email-search",
      "name": "❓ Need Email Search?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [1020, 300]
    },
    {
      "parameters": {\n        \"command\": \"python3 /data/scripts/enhanced_email_finder.py\",\n        \"additionalFields\": {\n          \"workingDirectory\": \"/data\"\n        }\n      },\n      \"id\": \"enhanced-email-search\",\n      \"name\": \"📧 Enhanced Email Search\",\n      \"type\": \"n8n-nodes-base.executeCommand\",\n      \"typeVersion\": 1,\n      \"position\": [1240, 200]\n    },\n    {\n      \"parameters\": {\n        \"jsCode\": \"// Enhanced email finding using web scraping and pattern generation\\nconst inputData = $input.all();\\nconst results = [];\\n\\nfor (const item of inputData) {\\n  const business = item.json;\\n  \\n  // Generate email patterns for businesses without found emails\\n  const generateEmailPatterns = (businessName, website) => {\\n    if (!website) return [];\\n    \\n    try {\\n      const domain = new URL(website).hostname.replace('www.', '');\\n      const patterns = [\\n        `info@${domain}`,\\n        `contact@${domain}`,\\n        `hello@${domain}`,\\n        `sales@${domain}`,\\n        `support@${domain}`,\\n        `admin@${domain}`,\\n        `office@${domain}`\\n      ];\\n      \\n      // Business name based patterns\\n      const cleanName = businessName.toLowerCase()\\n        .replace(/[^a-z0-9]/g, '')\\n        .substring(0, 15);\\n      \\n      if (cleanName) {\\n        patterns.push(`${cleanName}@${domain}`);\\n      }\\n      \\n      return patterns;\\n    } catch (e) {\\n      return [];\\n    }\\n  };\\n  \\n  // Merge existing data with enhanced email information\\n  const enhancedBusiness = {\\n    ...business,\\n    email_patterns: generateEmailPatterns(business.business_name, business.website),\\n    total_email_candidates: (business.emails_found || []).length,\\n    enhanced_search_complete: true,\\n    enhanced_at: new Date().toISOString()\\n  };\\n  \\n  // Update lead score based on email patterns\\n  if (enhancedBusiness.email_patterns.length > 0) {\\n    enhancedBusiness.lead_score += 10;\\n    enhancedBusiness.email_sources.push('pattern_generation');\\n  }\\n  \\n  // Recalculate lead quality\\n  const score = enhancedBusiness.lead_score;\\n  if (score >= 80) enhancedBusiness.lead_quality = 'High';\\n  else if (score >= 60) enhancedBusiness.lead_quality = 'Medium';\\n  else if (score >= 40) enhancedBusiness.lead_quality = 'Low';\\n  else enhancedBusiness.lead_quality = 'Poor';\\n  \\n  results.push(enhancedBusiness);\\n}\\n\\nreturn results.map(item => ({ json: item }));\"\n      },\n      \"id\": \"pattern-email-generation\",\n      \"name\": \"🔄 Generate Email Patterns\",\n      \"type\": \"n8n-nodes-base.code\",\n      \"typeVersion\": 2,\n      \"position\": [1240, 400]\n    },\n    {\n      \"parameters\": {\n        \"jsCode\": \"// Merge results from both email search paths\\nconst inputData = $input.all();\\nconst mergedResults = [];\\n\\n// Group by business name to avoid duplicates\\nconst businessMap = new Map();\\n\\nfor (const item of inputData) {\\n  const business = item.json;\\n  const key = business.business_name + '|' + business.address;\\n  \\n  if (!businessMap.has(key)) {\\n    businessMap.set(key, business);\\n  } else {\\n    // Merge email data from multiple sources\\n    const existing = businessMap.get(key);\\n    \\n    // Combine emails\\n    const allEmails = new Set([\\n      ...(existing.emails_found || []),\\n      ...(business.emails_found || [])\\n    ]);\\n    \\n    // Combine patterns\\n    const allPatterns = new Set([\\n      ...(existing.email_patterns || []),\\n      ...(business.email_patterns || [])\\n    ]);\\n    \\n    // Combine sources\\n    const allSources = new Set([\\n      ...(existing.email_sources || []),\\n      ...(business.email_sources || [])\\n    ]);\\n    \\n    // Update existing record\\n    existing.emails_found = Array.from(allEmails);\\n    existing.email_patterns = Array.from(allPatterns);\\n    existing.email_sources = Array.from(allSources);\\n    existing.total_email_candidates = allEmails.size;\\n    \\n    // Take the higher lead score\\n    existing.lead_score = Math.max(existing.lead_score, business.lead_score);\\n  }\\n}\\n\\n// Convert map back to array\\nfor (const [key, business] of businessMap) {\\n  // Final lead quality determination\\n  const score = business.lead_score;\\n  if (score >= 80) business.lead_quality = 'High';\\n  else if (score >= 60) business.lead_quality = 'Medium';\\n  else if (score >= 40) business.lead_quality = 'Low';\\n  else business.lead_quality = 'Poor';\\n  \\n  // Mark as ready for email generation if we have emails or patterns\\n  business.ready_for_email_generation = \\n    (business.emails_found && business.emails_found.length > 0) ||\\n    (business.email_patterns && business.email_patterns.length > 0);\\n  \\n  business.merged_at = new Date().toISOString();\\n  mergedResults.push(business);\\n}\\n\\n// Sort by lead score (highest first)\\nmergedResults.sort((a, b) => b.lead_score - a.lead_score);\\n\\nconsole.log(`Merged ${mergedResults.length} unique businesses`);\\nreturn mergedResults.map(item => ({ json: item }));\"\\n      },\\n      \"id\": \"merge-email-results\",\\n      \"name\": \"🔀 Merge & Deduplicate\",\\n      \"type\": \"n8n-nodes-base.code\",\\n      \"typeVersion\": 2,\\n      \"position\": [1460, 300]\\n    },\\n    {\\n      \"parameters\": {\\n        \"conditions\": {\\n          \"options\": {\\n            \"caseSensitive\": true,\\n            \"leftValue\": \"\",\\n            \"typeValidation\": \"strict\"\\n          },\\n          \"conditions\": [\\n            {\\n              \"id\": \"ready-for-email\",\\n              \"leftValue\": \"={{ $json.ready_for_email_generation }}\",\\n              \"rightValue\": true,\\n              \"operator\": {\\n                \"type\": \"boolean\",\\n                \"operation\": \"true\"\\n              }\\n            }\\n          ],\\n          \"combinator\": \"and\"\\n        },\\n        \"options\": {}\\n      },\\n      \"id\": \"filter-ready-businesses\",\\n      \"name\": \"✅ Filter Ready Businesses\",\\n      \"type\": \"n8n-nodes-base.if\",\\n      \"typeVersion\": 2,\\n      \"position\": [1680, 300]\\n    },\\n    {\\n      \"parameters\": {\\n        \"model\": \"llama3.1:latest\",\\n        \"options\": {\\n          \"baseURL\": \"http://localhost:11434\"\\n        },\\n        \"prompt\": \"=Generate a personalized cold email for the following business:\\n\\nBusiness: {{ $json.business_name }}\\nWebsite: {{ $json.website }}\\nPhone: {{ $json.phone }}\\nAddress: {{ $json.address }}\\nRating: {{ $json.rating }}/5 ({{ $json.review_count }} reviews)\\nLead Score: {{ $json.lead_score }}\\n\\nEmail Contact Options:\\n{% if $json.emails_found.length > 0 %}Found Emails: {{ $json.emails_found.join(', ') }}{% endif %}\\n{% if $json.email_patterns.length > 0 %}Email Patterns: {{ $json.email_patterns.slice(0,3).join(', ') }}{% endif %}\\n\\nCreate a professional, personalized cold email that:\\n1. Shows genuine research about their business\\n2. Offers specific value relevant to their industry\\n3. Has a clear, non-pushy call-to-action\\n4. Keeps it concise (under 150 words)\\n5. Uses a warm, professional tone\\n\\nFormat the response as:\\nSUBJECT: [compelling subject line]\\n\\nDear [Business Name] Team,\\n\\n[Email body here]\\n\\nBest regards,\\n[Your Name]\\n[Your Title]\\n[Your Company]\"\\n      },\\n      \"id\": \"generate-email-content\",\\n      \"name\": \"🤖 Generate Email with Ollama\",\\n      \"type\": \"n8n-nodes-base.openAi\",\\n      \"typeVersion\": 1,\\n      \"position\": [1900, 300]\\n    },\\n    {\\n      \"parameters\": {\\n        \"jsCode\": \"// Parse AI-generated email and prepare for Gmail draft creation\\nconst inputData = $input.all();\\nconst results = [];\\n\\nfor (const item of inputData) {\\n  const business = item.json;\\n  const aiContent = business.message?.content || '';\\n  \\n  // Parse subject and body from AI response\\n  let emailSubject = '';\\n  let emailBody = '';\\n  \\n  try {\\n    // Extract subject line\\n    const subjectMatch = aiContent.match(/SUBJECT:\\\\s*(.+?)\\\\n/i);\\n    if (subjectMatch) {\\n      emailSubject = subjectMatch[1].trim();\\n    } else {\\n      emailSubject = `Partnership Opportunity - ${business.business_name}`;\\n    }\\n    \\n    // Extract email body (everything after Dear until Best regards)\\n    const bodyMatch = aiContent.match(/Dear[\\\\s\\\\S]*?(?=Best regards|Sincerely|Kind regards)/i);\\n    if (bodyMatch) {\\n      emailBody = bodyMatch[0].trim();\\n    } else {\\n      // Fallback: use entire content\\n      emailBody = aiContent;\\n    }\\n    \\n  } catch (e) {\\n    console.log(`Error parsing AI content: ${e.message}`);\\n    emailSubject = `Partnership Opportunity - ${business.business_name}`;\\n    emailBody = aiContent;\\n  }\\n  \\n  // Determine recipient email (prioritize found emails over patterns)\\n  let recipientEmail = '';\\n  if (business.emails_found && business.emails_found.length > 0) {\\n    recipientEmail = business.emails_found[0]; // Use first found email\\n  } else if (business.email_patterns && business.email_patterns.length > 0) {\\n    recipientEmail = business.email_patterns[0]; // Use first pattern\\n  }\\n  \\n  // Create draft data\\n  const draftData = {\\n    // Business information\\n    business_name: business.business_name,\\n    business_address: business.address,\\n    business_phone: business.phone,\\n    business_website: business.website,\\n    business_rating: business.rating,\\n    lead_score: business.lead_score,\\n    lead_quality: business.lead_quality,\\n    \\n    // Email information\\n    recipient_email: recipientEmail,\\n    email_subject: emailSubject,\\n    email_body: emailBody,\\n    email_sources: business.email_sources || [],\\n    \\n    // Gmail draft specifics\\n    draft_status: 'pending_approval',\\n    requires_review: true,\\n    \\n    // Metadata\\n    generated_at: new Date().toISOString(),\\n    workflow_id: business.workflow_id || 'unknown'\\n  };\\n  \\n  results.push(draftData);\\n}\\n\\nconsole.log(`Prepared ${results.length} email drafts for approval`);\\nreturn results.map(item => ({ json: item }));\"\\n      },\\n      \"id\": \"prepare-draft-data\",\\n      \"name\": \"📝 Prepare Draft Data\",\\n      \"type\": \"n8n-nodes-base.code\",\\n      \"typeVersion\": 2,\\n      \"position\": [2120, 300]\\n    },\\n    {\\n      \"parameters\": {\\n        \"operation\": \"draft\",\\n        \"message\": {\\n          \"to\": \"={{ $json.recipient_email }}\",\\n          \"subject\": \"={{ $json.email_subject }}\",\\n          \"emailType\": \"text\",\\n          \"message\": \"={{ $json.email_body }}\"\\n        },\\n        \"additionalFields\": {\\n          \"ccList\": \"\",\\n          \"bccList\": \"\"\\n        }\\n      },\\n      \"id\": \"create-gmail-draft\",\\n      \"name\": \"📧 Create Gmail Draft\",\\n      \"type\": \"n8n-nodes-base.gmail\",\\n      \"typeVersion\": 1,\\n      \"position\": [2340, 300]\\n    },\\n    {\\n      \"parameters\": {\\n        \"operation\": \"appendOrUpdate\",\\n        \"documentId\": \"{{ $vars.GOOGLE_SHEETS_ID || '1wQs8kNqL5xP7yRvFt6J8K2mN9oP3qR4sT5uV6wX7yZ8a' }}\",\\n        \"sheetName\": \"Email Drafts for Review\",\\n        \"columns\": {\\n          \"mappingMode\": \"defineBelow\",\\n          \"value\": {\\n            \"Timestamp\": \"={{ $json.generated_at }}\",\\n            \"Business Name\": \"={{ $json.business_name }}\",\\n            \"Recipient Email\": \"={{ $json.recipient_email }}\",\\n            \"Email Subject\": \"={{ $json.email_subject }}\",\\n            \"Lead Score\": \"={{ $json.lead_score }}\",\\n            \"Lead Quality\": \"={{ $json.lead_quality }}\",\\n            \"Business Website\": \"={{ $json.business_website }}\",\\n            \"Business Phone\": \"={{ $json.business_phone }}\",\\n            \"Draft Status\": \"={{ $json.draft_status }}\",\\n            \"Gmail Draft ID\": \"={{ $json.id }}\",\\n            \"Review Required\": \"YES\",\\n            \"Approved\": \"PENDING\",\\n            \"Email Sources\": \"={{ $json.email_sources.join(', ') }}\"\\n          }\\n        },\\n        \"options\": {}\\n      },\\n      \"id\": \"save-drafts-to-sheets\",\\n      \"name\": \"📊 Save to Review Sheet\",\\n      \"type\": \"n8n-nodes-base.googleSheets\",\\n      \"typeVersion\": 4,\\n      \"position\": [2560, 300]\\n    },\\n    {\\n      \"parameters\": {\\n        \"operation\": \"sendMessage\",\\n        \"chatId\": \"{{ $vars.TELEGRAM_CHAT_ID || 'your-telegram-chat-id' }}\",\\n        \"text\": \"=🎯 **Cold Email Workflow Complete!**\\n\\n📊 **Summary:**\\n• Total Businesses Processed: {{ $input.all().length }}\\n• Email Drafts Created: {{ $input.all().filter(item => item.json.id).length }}\\n• High Quality Leads: {{ $input.all().filter(item => item.json.lead_quality === 'High').length }}\\n\\n📧 **Gmail Drafts Created:**\\n{{ $input.all().slice(0,5).map((item, i) => `${i+1}. ${item.json.business_name} - ${item.json.lead_quality} Quality`).join('\\\\n') }}\\n\\n✅ **Next Steps:**\\n1. Review drafts in Gmail\\n2. Approve/edit emails in Google Sheets\\n3. Send approved emails\\n\\n🔗 **Links:**\\n• Gmail Drafts: https://mail.google.com/mail/u/0/#drafts\\n• Review Sheet: https://docs.google.com/spreadsheets/d/{{ $vars.GOOGLE_SHEETS_ID || '1wQs8kNqL5xP7yRvFt6J8K2mN9oP3qR4sT5uV6wX7yZ8a' }}\"\\n      },\\n      \"id\": \"notification-telegram\",\\n      \"name\": \"📱 Telegram Notification\",\\n      \"type\": \"n8n-nodes-base.telegram\",\\n      \"typeVersion\": 1,\\n      \"position\": [2780, 200]\\n    },\\n    {\\n      \"parameters\": {\\n        \"to\": \"{{ $vars.NOTIFICATION_EMAIL || '<EMAIL>' }}\",\\n        \"subject\": \"Cold Email Workflow Complete - {{ $input.all().length }} Leads Processed\",\\n        \"emailType\": \"html\",\\n        \"message\": \"=<h2>🎯 Cold Email Workflow Summary</h2>\\n\\n<h3>📊 Processing Results:</h3>\\n<ul>\\n<li><strong>Total Businesses:</strong> {{ $input.all().length }}</li>\\n<li><strong>Gmail Drafts Created:</strong> {{ $input.all().filter(item => item.json.id).length }}</li>\\n<li><strong>High Quality Leads:</strong> {{ $input.all().filter(item => item.json.lead_quality === 'High').length }}</li>\\n<li><strong>Medium Quality Leads:</strong> {{ $input.all().filter(item => item.json.lead_quality === 'Medium').length }}</li>\\n</ul>\\n\\n<h3>📧 Top Leads (Ready for Review):</h3>\\n<table border='1' style='border-collapse: collapse;'>\\n<tr>\\n<th>Business Name</th>\\n<th>Email</th>\\n<th>Lead Score</th>\\n<th>Quality</th>\\n</tr>\\n{{ $input.all().slice(0,10).map(item => `<tr><td>${item.json.business_name}</td><td>${item.json.recipient_email}</td><td>${item.json.lead_score}</td><td>${item.json.lead_quality}</td></tr>`).join('') }}\\n</table>\\n\\n<h3>✅ Next Actions:</h3>\\n<ol>\\n<li><a href='https://mail.google.com/mail/u/0/#drafts'>Review Gmail Drafts</a></li>\\n<li><a href='https://docs.google.com/spreadsheets/d/{{ $vars.GOOGLE_SHEETS_ID || '1wQs8kNqL5xP7yRvFt6J8K2mN9oP3qR4sT5uV6wX7yZ8a' }}'>Approve in Google Sheets</a></li>\\n<li>Send approved emails manually or via automation</li>\\n</ol>\\n\\n<p><em>Generated at: {{ new Date().toISOString() }}</em></p>\"\\n      },\\n      \"id\": \"email-notification\",\\n      \"name\": \"📧 Email Summary\",\\n      \"type\": \"n8n-nodes-base.emailSend\",\\n      \"typeVersion\": 1,\\n      \"position\": [2780, 400]\\n    },\\n    {\\n      \"parameters\": {\\n        \"respondWith\": \"json\",\\n        \"responseBody\": \"={\\n  \\\"status\\\": \\\"success\\\",\\n  \\\"message\\\": \\\"Cold email workflow completed successfully\\\",\\n  \\\"results\\\": {\\n    \\\"total_businesses_processed\\\": {{ $input.all().length }},\\n    \\\"gmail_drafts_created\\\": {{ $input.all().filter(item => item.json.id).length }},\\n    \\\"high_quality_leads\\\": {{ $input.all().filter(item => item.json.lead_quality === 'High').length }},\\n    \\\"medium_quality_leads\\\": {{ $input.all().filter(item => item.json.lead_quality === 'Medium').length }},\\n    \\\"workflow_type\\\": \\\"open_source_with_gmail_drafts\\\",\\n    \\\"approval_required\\\": true\\n  },\\n  \\\"next_steps\\\": [\\n    \\\"Review Gmail drafts at https://mail.google.com/mail/u/0/#drafts\\\",\\n    \\\"Approve emails in Google Sheets\\\",\\n    \\\"Send approved emails manually or set up approval automation\\\"\\n  ],\\n  \\\"links\\\": {\\n    \\\"gmail_drafts\\\": \\\"https://mail.google.com/mail/u/0/#drafts\\\",\\n    \\\"review_sheet\\\": \\\"https://docs.google.com/spreadsheets/d/{{ $vars.GOOGLE_SHEETS_ID || '1wQs8kNqL5xP7yRvFt6J8K2mN9oP3qR4sT5uV6wX7yZ8a' }}\\\"\\n  },\\n  \\\"timestamp\\\": \\\"{{ new Date().toISOString() }}\\\",\\n  \\\"workflow_version\\\": \\\"v3.0-opensource-gmail-drafts\\\"\\n}\"\\n      },\\n      \"id\": \"webhook-response\",\\n      \"name\": \"✅ Success Response\",\\n      \"type\": \"n8n-nodes-base.respondToWebhook\",\\n      \"typeVersion\": 1,\\n      \"position\": [3000, 300]\\n    }\\n  ],\\n  \\\"connections\\\": {\\n    \\\"🚀 Start Cold Email Workflow\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"📝 Process Input & Validate\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"📝 Process Input & Validate\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"🗺️ Google Maps with Email Extraction\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"🗺️ Google Maps with Email Extraction\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"🔍 Parse Business Results\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"🔍 Parse Business Results\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"❓ Need Email Search?\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"❓ Need Email Search?\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"📧 Enhanced Email Search\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ],\\n        [\\n          {\\n            \\\"node\\\": \\\"🔄 Generate Email Patterns\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"📧 Enhanced Email Search\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"🔀 Merge & Deduplicate\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"🔄 Generate Email Patterns\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"🔀 Merge & Deduplicate\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"🔀 Merge & Deduplicate\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"✅ Filter Ready Businesses\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"✅ Filter Ready Businesses\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"🤖 Generate Email with Ollama\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"🤖 Generate Email with Ollama\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"📝 Prepare Draft Data\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"📝 Prepare Draft Data\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"📧 Create Gmail Draft\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"📧 Create Gmail Draft\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"📊 Save to Review Sheet\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    },\\n    \\\"📊 Save to Review Sheet\\\": {\\n      \\\"main\\\": [\\n        [\\n          {\\n            \\\"node\\\": \\\"📱 Telegram Notification\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          },\\n          {\\n            \\\"node\\\": \\\"📧 Email Summary\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          },\\n          {\\n            \\\"node\\\": \\\"✅ Success Response\\\",\\n            \\\"type\\\": \\\"main\\\",\\n            \\\"index\\\": 0\\n          }\\n        ]\\n      ]\\n    }\\n  },\\n  \\\"settings\\\": {\\n    \\\"executionOrder\\\": \\\"v1\\\"\\n  },\\n  \\\"staticData\\\": null,\\n  \\\"tags\\\": [\\\"cold-email\\\", \\\"open-source\\\", \\\"gmail-drafts\\\", \\\"approval-workflow\\\"],\\n  \\\"triggerCount\\\": 0,\\n  \\\"updatedAt\\\": \\\"2025-08-08T10:00:00.000Z\\\",\\n  \\\"versionId\\\": \\\"v3.0-complete-opensource\\\"\\n}"
